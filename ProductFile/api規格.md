# BrainHub API 規格文檔

## 更新記錄

### 2025-07-29 (最新重構 - genai.Chat 實現)
- **Gemini genai.Chat 原生對話管理重構完成**：
  - 將 Gemini 實現從手動歷史管理遷移到官方 `genai.Chat` API 的原生對話管理
  - **genai.Chat 集成**：使用官方 `genai.Chat` 實例替代 `garray.Array` 歷史管理，提升對話一致性
  - **線程安全優化**：利用 `genai.Chat` 的內建線程安全機制，移除手動同步控制
  - **Token 監控機制**：實現粗略的 token 使用量估算，支持自動歷史總結以控制 token 消耗
  - **智能歷史管理**：當 token 使用量接近閾值（8000）時，自動觸發對話歷史總結功能
  - **對話狀態管理**：通過重新創建 `genai.Chat` 實例實現歷史清空和對話重置
  - **附件處理優化**：將附件信息直接發送到 `genai.Chat` 作為對話上下文
  - **錯誤處理統一**：實現 `mapGenAIError()` 方法，將 genai SDK 錯誤統一映射為 GoFrame 錯誤類型
  - **代理配置保持**：繼續支持完整的代理配置，包括連接池和 SSL 選項
  - **認證機制不變**：繼續使用標準 `GOOGLE_APPLICATION_CREDENTIALS` 環境變量進行認證
  - **測試覆蓋完整**：更新所有單元測試和整合測試以適配新的 genai.Chat 實現
  - **向後兼容性**：保持所有公開接口不變，確保與現有系統的完全兼容

### 2025-07-29 (舊版本)
- **Gemini 實現模式重構**：
  - 將 Gemini 實現從 genai SDK 重構為 HTTP 客戶端模式，與 Claude 服務保持一致
  - **HTTP 客戶端統一**：使用 GoFrame 的 `*gclient.Client` 替代 `*genai.Client`
  - **API 調用方式統一**：採用標準的 HTTP RESTful API 調用，直接調用 Google Vertex AI REST API
  - **結構體字段對齊**：與 Claude 服務保持相同的基礎字段結構（httpClient、endpoint、accessToken 等）
  - **方法實現風格統一**：所有方法實現風格與 Claude 服務保持一致
  - **YouTube 連結支持**：在 `processAttachments` 方法中新增對 YouTube 連結的支持
  - **線程安全優化**：使用 `garray.Array` 管理對話歷史記錄，確保線程安全
  - **OAuth2 認證統一**：採用與 Claude 相同的 OAuth2 token 管理和刷新機制
  - **錯誤處理統一**：統一的重試邏輯和錯誤處理策略
  - **資源管理優化**：改進資源釋放和清理機制
  - **Proxy 配置自動化**：在初始化時自動讀取 `system.proxy` 配置並設置代理
    - 支援在 `Initialize` 方法中自動配置 HTTP 客戶端代理
    - 在每次 API 請求時動態設置代理，確保網絡連接穩定
    - 與 Claude 服務保持完全一致的代理配置機制
  - **YouTube 視頻處理優化**：根據 Gemini API 官方文檔更新 YouTube 連結處理
    - 使用正確的 `file_data` 格式，包含 `file_uri` 字段
    - 利用 Gemini API 原生視頻處理能力，支援視頻內容分析
    - 支援多種 YouTube URL 格式（youtube.com、youtu.be、shorts 等）
    - 每秒視頻約消耗 300 tokens（默認解析度）或 100 tokens（低解析度）

## Gemini genai.Chat 原生對話管理技術實現細節

### 核心架構變更
- **SDK 版本**：使用 `google.golang.org/genai v1.17.0`
- **後端配置**：使用 `genai.BackendVertexAI` 後端
- **客戶端初始化**：通過 `genai.NewClient(ctx, clientConfig)` 創建客戶端
- **對話管理**：使用 `genai.Chat` 實例進行原生對話管理，替代手動歷史記錄
- **內容格式**：使用 `genai.Content` 和 `genai.Part` 結構體

### genai.Chat 對話管理機制
- **Chat 實例創建**：通過 `client.Chats.Create(ctx, modelName, config, history)` 創建對話實例
- **消息發送**：使用 `chat.SendMessage(ctx, part)` 發送消息並獲取回應
- **歷史管理**：genai.Chat 自動管理對話歷史，無需手動維護消息列表
- **線程安全**：genai.Chat 內建線程安全機制，支持併發訪問
- **狀態重置**：通過重新創建 Chat 實例實現歷史清空和對話重置

### Token 使用量監控與歷史管理策略
- **Token 估算**：實現粗略的 token 使用量估算機制（基於字符數量）
- **閾值監控**：設置 token 使用量閾值（默認 8000），接近時觸發歷史總結
- **自動總結**：當 token 使用量超過閾值時，自動請求 AI 生成對話摘要
- **歷史壓縮**：將對話摘要作為新的對話起點，有效控制 token 消耗
- **計數器重置**：總結完成後重置 token 計數器，開始新的計數週期

### 主要方法實現
1. **initializeGenAIClient**：初始化 genai 客戶端，包含代理配置
2. **setupAdvancedGenerationParams**：設置 TopP 和 TopK 參數
3. **validateProxyConfiguration**：驗證代理配置
4. **convertHistoryToGenAIContent**：轉換歷史記錄格式
5. **convertAssetToGenAIParts**：轉換附件格式
6. **sendGenAIRequest**：發送 API 請求
7. **mapGenAIError**：錯誤映射和處理

### 配置參數支持
- **Temperature**：生成溫度控制
- **MaxOutputTokens**：最大輸出 token 數量
- **TopP**：核採樣參數（新增）
- **TopK**：Top-K 採樣參數（新增）

### 代理配置
- **HTTP 客戶端**：自定義 HTTP 客戶端支持代理
- **連接池**：配置連接池大小和超時
- **SSL 選項**：支持 SSL 配置和證書驗證

## YouTube 視頻處理技術細節

### 支援的 YouTube URL 格式
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://youtube.com/watch?v=VIDEO_ID`
- `https://m.youtube.com/watch?v=VIDEO_ID`
- `https://youtube.com/shorts/VIDEO_ID`
- `https://youtube.com/embed/VIDEO_ID`
- `https://youtube.com/v/VIDEO_ID`

### API 請求格式
根據 [Gemini API 官方文檔](https://ai.google.dev/gemini-api/docs/video-understanding#youtube)，YouTube 連結使用以下格式：

```json
{
  "role": "user",
  "parts": [
    {
      "file_data": {
        "file_uri": "https://www.youtube.com/watch?v=VIDEO_ID",
        "mime_type": "video/mp4"
      }
    }
  ]
}
```

**重要修復（2025-07-29）：**
- 修復了 `mimeType` 參數為空導致的 API 錯誤
- 根據 Gemini API 官方文檔，`file_data` 必須包含 `mime_type` 參數
- YouTube 視頻統一使用 `video/mp4` 作為 MIME 類型
- **環境變量自動設置**：在初始化和每次 API 請求時自動設置 `GOOGLE_APPLICATION_CREDENTIALS` 環境變量
  - 使用與 `Initialize` 方法一致的憑證文件路徑構建邏輯
  - 優先使用 `system.vertex_key_path` 配置結合 `credential_file` 構建完整路徑
  - 提供降級處理：當系統配置不可用時使用相對路徑
  - 確保 Google Cloud SDK 能夠正確找到憑證文件
  - 提供詳細的日誌記錄，便於調試認證問題
- **詳細調試日誌**：在 `sendAPIRequest` 方法中新增完整的調試信息
  - 記錄完整的 API 請求體（JSON 格式化輸出）
  - 記錄所有 HTTP 請求標頭（敏感信息遮罩處理）
  - 記錄環境變量設置狀態和代理配置信息
  - 請求體過長時自動截斷，避免日誌過於冗長

### 限制條件
- **免費版**：每天最多 8 小時 YouTube 視頻
- **付費版**：無視頻長度限制
- **視頻類型**：僅支援公開視頻（非私人或未列出）
- **並發限制**：Gemini 2.5+ 每次請求最多 10 個視頻，之前版本最多 1 個

### Token 消耗
- **默認解析度**：約 300 tokens/秒
- **低解析度**：約 100 tokens/秒
- **處理方式**：1 FPS 視頻採樣 + 1Kbps 音頻處理

## 重構完成狀態

✅ **已完成**：Gemini 服務已成功重構為使用官方 `google.golang.org/genai` SDK 實現
✅ **SDK 集成**：完整集成 genai SDK，支持 Vertex AI 和 Gemini API 雙後端
✅ **測試通過**：所有單元測試、整合測試正常運行，包括新增的 genai SDK 功能測試
✅ **編譯正常**：代碼編譯無錯誤，所有依賴正確配置
✅ **功能完整**：實現了所有必要的 LLM 接口方法，支持進階參數配置
✅ **YouTube 支援**：使用 genai SDK 原生支持 YouTube 視頻處理功能
✅ **Proxy 配置**：完整的代理配置支持，包括連接池和 SSL 選項
✅ **線程安全**：使用 garray.Array 確保併發安全
✅ **資源管理**：正確實現 genai 客戶端的生命週期管理
✅ **錯誤處理**：全面的錯誤映射和重試機制
✅ **認證機制**：標準 GOOGLE_APPLICATION_CREDENTIALS 環境變量認證

---

# 更新日誌

### 2025-07-29
- **YouTube 視頻處理優化**：
  - 根據 Gemini API 官方文檔更新 YouTube 連結處理邏輯
  - 使用正確的 `file_data` 格式替代文本包裝方式
  - 利用 Gemini API 原生視頻處理能力，支援視頻內容分析
  - 新增完整的 YouTube URL 格式驗證和測試覆蓋
  - 支援多種 YouTube URL 格式（youtube.com、youtu.be、shorts 等）

- **Proxy 配置自動化完成**：
  - 在 `Initialize` 方法中自動讀取 `system.proxy` 配置
  - 在 `sendAPIRequest` 方法中動態設置代理
  - 與 Claude 服務保持完全一致的代理配置機制
  - 提供詳細的日誌記錄，便於調試網絡連接問題

### 2025-07-28
- **Google Vertex AI 授權認證修復**：
  - 修復 Google GenAI Go SDK 授權配置問題，添加必要的 HTTPOptions.APIVersion 設置
  - 新增客戶端授權驗證機制，在初始化時驗證授權是否正確配置
  - 改進錯誤處理和日誌記錄，提供更詳細的授權失敗信息
  - 支援多種授權方式：環境變數、配置文件、預設憑證

### 2025-01-28
- **Claude Token 管理優化**：
  - 新增自動 token 刷新機制，避免 Google Cloud OAuth2 token 過期導致的 API 調用失敗
  - 改進認證錯誤檢測，當檢測到 401/403 錯誤時自動刷新 access token
  - 增強重試邏輯，在 token 刷新後立即重試請求，提升服務穩定性

- **Gemini 代理配置修復**：
  - 新增 Gemini 客戶端代理支持，解決無法連接 Google Cloud API 的問題
  - 在 `genai.NewClient` 初始化時自動配置代理設置
  - 支持從配置文件 `system.proxy` 讀取代理地址，與 Claude 保持一致
  - 修復網絡連接問題，確保在需要代理的環境中正常工作

- **Gemini 憑證文件優先級優化**：
  - 優先使用配置參數中指定的憑證文件，即使環境變量已設置
  - 當配置中未指定憑證文件時，才使用環境變量 `GOOGLE_APPLICATION_CREDENTIALS`
  - 改進錯誤處理，提供更清晰的憑證文件缺失提示
  - 確保憑證文件配置的靈活性和可控性

## 概述

BrainHub 是一個基於 GoFrame 框架構建的 AI 聊天和文件處理後端服務，提供多種 AI 模型整合、文件處理、向量嵌入等功能。

## 更新記錄

### 2025-01-20 - Claude API 消息體結構修正
- **消息體格式修正**：修正 `ContentPart` 結構體的 JSON 序列化，確保完全符合 Google Vertex AI Claude API 規範
- **結構體改進**：
  - 為 `ContentPart` 添加自定義 `MarshalJSON()` 方法，根據 `type` 字段只包含相關字段
  - 文本類型 (`type: "text"`) 只包含 `type` 和 `text` 字段
  - 圖片類型 (`type: "image"`) 只包含 `type` 和 `source` 字段
  - 移除了導致 "Extra inputs are not permitted" 錯誤的多餘字段
- **序列化優化**：
  - 為 `ClaudeMessage` 添加自定義序列化方法，確保嵌套的 `ContentPart` 正確序列化
  - 為 `ClaudeRequest` 添加自定義序列化方法，確保整個請求體格式正確
  - 修改 `sendClaudeRequest()` 方法使用自定義序列化而非 `gjson.New()`
- **測試驗證**：
  - 添加 `TestContentPartSerialization` 測試驗證 ContentPart 序列化
  - 更新 `TestClaudeRequestFormat` 測試使用自定義序列化方法
  - 確保生成的 JSON 完全符合 API 規範，不包含不必要的 null 字段
- **API 規範符合性**：完全符合 Google Vertex AI Claude API 官方文檔要求

### 2025-01-20 - Omnichannel Chat Nil Pointer 修正
- **Nil 檢查完善**：在 `omnichannel_v1_chat.go` 中添加全面的 nil 檢查
- **安全性改進**：
  - 在 `ai.Chat()` 調用後檢查 `resp` 是否為 nil
  - 在錯誤處理分支中確保 `resp` 不為 nil
  - 在成功分支中也添加 nil 檢查，避免後續處理中的 panic
  - 在函數結尾添加最終的 nil 檢查，確保 `utility.ConvertAnyToGenericMessage()` 調用安全
- **錯誤處理優化**：當 `resp` 為 nil 時，創建預設的 `ResponseData` 結構體
- **日誌記錄改進**：移除不必要的 nil 檢查，簡化 `gtime.FuncCost()` 的使用
- **問題修正**：解決 runtime panic "invalid memory address or nil pointer dereference" 錯誤

### 2025-01-20 - Claude API 請求格式修正
- **API 版本修正**：將預設 `anthropic_version` 從 `"2023-06-01"` 修正為 `"vertex-2023-10-16"`
- **請求格式規範化**：
  - 添加 `stream` 參數到 `ClaudeRequest` 結構體，符合 Google Vertex AI Claude API 規範
  - 確保請求體包含所有必需字段：`anthropic_version`、`max_tokens`、`temperature`、`messages`、`stream`
- **代理配置完善**：
  - 在 `New()` 函數中設置 GoFrame HTTP 客戶端代理：`http://127.0.0.1:7890`
  - 在 `sendClaudeRequest()` 中確保 API 請求透過代理
  - 在 OAuth2 token 獲取中設置自定義 HTTP 客戶端，確保認證請求也透過代理
  - 環境需要透過 proxy 連接所有外部 API
- **端點格式確認**：使用正確的 Google Vertex AI Claude API 端點格式 `:rawPredict`
- **調試改進**：添加請求體日誌記錄，便於問題排查
- **測試完善**：新增 `TestClaudeRequestFormat` 測試，驗證請求格式符合 API 規範
- **錯誤修正**：解決 HTTP 400 "Invalid request error" 問題，確保 API 調用成功

### 2025-01-17 - Claude 併發安全架構重構
- **數據結構升級**：將 Claude 結構體中的 `history []claude.ClaudeMessage` 改為 `history *garray.Array`
- **線程安全保證**：使用 GoFrame `garray.New(true)` 提供內建的併發安全機制
- **鎖機制簡化**：移除所有手動 `historyMutex sync.RWMutex` 鎖操作，避免死鎖風險
- **API 接口統一**：統一使用 `garray.Array` 的 `Append()`、`Slice()`、`Len()`、`Clear()` 方法
- **併發安全改進**：
  - `processAttachments` 方法移除手動鎖操作，利用 garray 內建線程安全
  - `calculateTokenCount` 方法使用 `Slice()` 獲取歷史記錄副本進行計算
  - `summarizeConversation` 方法安全地重置歷史記錄
  - `getResponse` 方法線程安全地添加和讀取消息
- **測試完善**：新增併發安全測試，包括死鎖預防測試，確保高併發環境下的穩定性
- **架構一致性**：Claude 現在與 AoAi 使用相同的併發安全模式，Gemini 使用 SDK 內建的線程安全機制

### 2025-01-17 - LLM 結構體 Payload 欄位優化與緩存邏輯改進
- **AoAi 結構體增強**：在 `AoAi` 結構體中新增 `payload` 欄位，用於儲存初始化時的 payload 資料
- **Payload 管理優化**：在 `Initialize` 方法中儲存 payload，供後續 token 管理和重新載入使用
- **Token 管理改進**：`checkAndManageTokensWithPayload` 方法優先使用傳入的 payload，若為 nil 則使用儲存的 payload
- **緩存邏輯優化**：在 `llmRouter.Select` 方法中新增緩存判斷邏輯
  - 從緩存中取出的 LLM 物件跳過 `Initialize` 方法調用
  - 新建立的 LLM 物件正常調用 `Initialize` 方法
- **資源清理完善**：在 `Release` 方法中清理 payload 引用，防止記憶體洩漏
- **測試更新**：更新相關測試案例，驗證 payload 欄位的正確性
- **架構一致性**：確保 Gemini 和 Claude 實現也遵循相同的 payload 管理模式

### 2025-01-14 - AoAi Token 管理優化
- **Token 閾值調整**：將 `AoAiTokenThreshold` 從 3000 提升至 15000，適配現代 GPT 模型
- **Token 緩存機制**：新增 Token 計算結果緩存，提升性能並減少重複計算
- **檢查頻率優化**：附件處理時的 Token 檢查間隔從 3-5 個調整為 10 個
- **錯誤處理改進**：Token 計算失敗時提供降級策略，避免服務中斷
- **併發安全增強**：改進 Token 緩存的併發安全機制

### 2025-01-15 - AoAi 併發安全架構重構
- **數據結構升級**：將 `history` 字段從 `[]langchainllms.MessageContent` 改為 `*garray.Array`
- **線程安全保證**：使用 GoFrame `garray.New(true)` 提供內建的併發安全機制
- **鎖機制簡化**：移除所有手動 `historyMutex` 鎖操作，避免死鎖風險
- **API 接口統一**：統一使用 `garray.Array` 的 `Append()`、`Slice()`、`Len()`、`Clear()` 方法
- **性能優化**：減少鎖競爭，提升併發性能
- **測試完善**：新增死鎖預防測試，確保高併發環境下的穩定性

### 基本資訊
- **服務名稱**: brainHub
- **框架**: GoFrame v2.9.0
- **預設端口**: 9001
- **API 版本**: v1
- **支援的內容類型**: application/json, multipart/form-data

### 服務架構
- **Brain API**: AI 聊天功能，支援文字和附件聊天
- **Omnichannel API**: 全管道聊天功能
- **Embedding API**: 文字向量嵌入服務

## 認證機制

目前系統採用基於租戶 ID (tenant_id) 和服務 ID (service_id) 的身份識別機制，無需額外的 API 金鑰認證。

### 必要參數
所有 API 請求都需要包含以下基本參數：
- `tenant_id`: 租戶識別碼
- `service_id`: 服務識別碼
- `user_id`: 使用者識別碼（部分 API）
- `channel`: 渠道標識（部分 API）

## 錯誤處理

### 標準響應格式
```json
{
  "code": 0,
  "message": "success"
}
```

### 錯誤碼定義
| 錯誤碼 | 說明 | 訊息 |
|--------|------|------|
| 0 | 成功 | success |
| 2000 | API 調用失敗 | failed to call api |
| 2001 | 不支援的上傳內容類型 | upload not supported content type |
| 2002 | 全管道聊天失敗 | 抱歉，當前網路連線好像有點不穩定，暫時無法回復。請稍候再試一次？ |
| 2003 | 網站爬取未完成 | 抱歉，資料還在準備中，請稍待馬上就好 |
| 2004 | 找不到內容 | The content is not exist |

## Brain API

### 1. 文字聊天

**端點**: `POST /v1/line/chat`

**描述**: 與 AI 模型進行文字對話

**請求參數**:
```json
{
  "tenant_id": "string",     // 必填：租戶 ID
  "user_id": "string",       // 必填：使用者 ID
  "service_id": "string",    // 必填：服務 ID
  "message": "string",       // 必填：聊天訊息
  "channel": "string",       // 必填：渠道標識
  "display_name": "string"   // 選填：顯示名稱
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "tenant_id": "string",
  "user_id": "string",
  "service_id": "string",
  "channel": "string",
  "response": "string",      // AI 回應內容
  "llm_name": "string",      // 使用的模型名稱
  "input_tokens": 100,       // 輸入 token 數量
  "output_tokens": 150,      // 輸出 token 數量
  "total_tokens": 250        // 總 token 數量
}
```

### 2. 附件聊天

**端點**: `POST /v1/line/chat/attachment`

**描述**: 上傳附件並與 AI 模型對話

**請求格式**: multipart/form-data

**請求參數**:
- `attachment`: file (必填) - 附件文件
- `user_id`: string (必填) - 使用者 ID
- `service_id`: string (必填) - 服務 ID
- `tenant_id`: string (必填) - 租戶 ID
- `mime`: string (必填) - 文件 MIME 類型
- `channel`: string (必填) - 渠道標識

**支援的文件格式**:
- **圖片**: image/jpeg, image/png, image/heic, image/heif, image/webp
- **影片**: video/mp4, video/3gpp, video/mpeg, video/mov, video/avi, video/x-flv, video/mpg, video/webm, video/wmv
- **音訊**: audio/wav, audio/mp3, audio/ogg, audio/aac, audio/aiff, audio/flac
- **文件**: application/pdf, text/plain, text/html, text/css, text/md, text/csv, text/xml, text/rtf
- **辦公文件**: pptx, docx, xlsx, xls

**文件大小限制**:
- 最小: 20MB
- 最大: 50MB
- 最大 token 數: 500,000

**響應格式**: 同文字聊天 API

### 3. 內容生成

**端點**: `POST /v1/brain/generate_content`

**描述**: 使用統一的 LLM 介面生成內容

**請求參數**:
```json
{
  "tenant_id": "string",   // 必填：租戶 ID
  "service_id": "string",  // 必填：服務 ID
  "channel": "string",     // 必填：渠道標識
  "prompt": "string"       // 必填：提示詞
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "llm_name": "string",              // 使用的模型名稱
    "output_content": "string",        // 生成的內容
    "input_tokens": 100,               // 輸入 token 數量
    "output_tokens": 200,              // 輸出 token 數量
    "total_tokens": 300,               // 總 token 數量
    "continuation_count": 0,           // 續寫次數
    "is_complete": true,               // 內容是否完整
    "generation_time": 1500,           // 生成耗時（毫秒）
    "thinking_process": "string",      // 思考過程（僅 Gemini）
    "safety_warnings": ["string"],     // 安全警告
    "finish_reason": "string"          // 完成原因
  }
}
```

## Omnichannel API

### 全管道聊天

**端點**: `POST /v1/omnichannel/chat`

**描述**: 全管道聊天功能，支援多種來源和版本的資料查詢

**請求參數**:
```json
{
  "tenant_id": "string",              // 必填：租戶 ID
  "question": "string",               // 必填：問題內容
  "source": ["string"],               // 選填：資料來源
  "version": ["string"],              // 選填：版本，預設 "*"
  "tags": ["string"],                 // 選填：標籤，預設 "*"
  "status": "string",                 // 選填：狀態，預設 "online"
  "channel": "string",                // 選填：渠道
  "service_id": "string",             // 選填：服務 ID
  "session_id": "string",             // 選填：會話 ID
  "message_type": "string",           // 選填：訊息類型
  "user_id": "string",                // 選填：使用者 ID
  "sensitive_words_enabled": false,   // 選填：是否啟用敏感詞檢測
  "voice_params": "string"            // 選填：語音參數
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "answer": "string",           // JSON 格式的回答內容
  "hit": true,                  // 是否命中
  "sensitive": false,           // 是否包含敏感內容
  "rel_id": ["string"],         // 相關 ID
  "answer_from": "string",      // 回答來源
  "sequence_id": "string",      // 序列 ID
  "answer_from_source": "string" // 回答來源詳情
}
```

**回答格式範例**:
```json
[
  {
    "quickReply": {
      "items": [
        {
          "action": {
            "data": "相關問題1",
            "displayText": "相關問題1",
            "title": "相關問題1",
            "type": "Postback"
          },
          "type": "Action"
        }
      ]
    },
    "text": "這是 AI 的回答內容",
    "type": "Text"
  }
]
```

## Embedding API

### 1. 生成向量嵌入

**端點**: `POST /v1/embedding/generate`

**描述**: 將文字轉換為向量嵌入

**請求參數**:
```json
{
  "tenant_id": "string",   // 必填：租戶 ID
  "service_id": "string",  // 必填：服務 ID
  "texts": ["string"],     // 必填：文字列表
  "model": "string"        // 選填：指定使用的模型
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "embeddings": [[0.1, 0.2, 0.3]],  // 向量列表
    "model": "string",                 // 實際使用的模型名稱
    "provider": "string",              // 提供商名稱
    "usage": {
      "prompt_tokens": 50,             // 提示 token 數量
      "total_tokens": 50               // 總 token 數量
    },
    "process_time": 500,               // 處理時間（毫秒）
    "dimensions": 768                  // 向量維度
  }
}
```

### 2. 健康狀態檢查

**端點**: `GET /v1/embedding/health`

**描述**: 檢查嵌入服務的健康狀態

**請求參數**:
```json
{
  "tenant_id": "string",   // 必填：租戶 ID
  "service_id": "string"   // 必填：服務 ID
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "healthy": true,        // 是否健康
    "provider": "string",   // 提供商名稱
    "model": "string",      // 模型名稱
    "check_time": ********** // 檢查時間戳
  }
}
```

### 3. 性能指標

**端點**: `GET /v1/embedding/metrics`

**描述**: 獲取嵌入服務的性能指標

**請求參數**:
```json
{
  "tenant_id": "string",   // 必填：租戶 ID
  "service_id": "string"   // 必填：服務 ID
}
```

**響應格式**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_requests": 1000,      // 總請求數
    "successful_requests": 950,   // 成功請求數
    "failed_requests": 50,        // 失敗請求數
    "average_response_time": 200, // 平均響應時間（毫秒）
    "total_tokens_processed": 50000, // 處理的總 token 數
    "uptime": 86400              // 運行時間（秒）
  }
}
```

## 系統配置

### 支援的 AI 模型
- **Gemini**: Google Vertex AI Gemini 模型
- **Claude**: Anthropic Claude 模型（透過 Vertex AI）
- **Azure OpenAI**: 微軟 Azure OpenAI 服務

### 文件存儲
- **類型**: Google Cloud Storage
- **支援操作**: 上傳、下載、刪除、列表

### 快取機制
- **Redis**: 用於快取租戶配置和 AI 模型實例
- **TTL 配置**:
  - AI 路由器 TTL: 2小時
  - AI 保留 TTL: 1小時
  - AI 發送重試 TTL: 40秒

## 中間件功能

### 請求/響應日誌
系統自動記錄所有 HTTP 請求和響應：
- 請求 URI 和請求體
- 響應內容
- 處理時間統計

### 錯誤處理
- 統一的錯誤響應格式
- 自動錯誤碼映射
- 詳細的錯誤日誌記錄

## 使用範例

### 基本聊天請求
```bash
curl -X POST http://localhost:9001/v1/line/chat \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "tenant123",
    "user_id": "user456",
    "service_id": "service789",
    "message": "你好，請介紹一下你的功能",
    "channel": "web"
  }'
```

### 附件上傳聊天
```bash
curl -X POST http://localhost:9001/v1/line/chat/attachment \
  -F "attachment=@document.pdf" \
  -F "tenant_id=tenant123" \
  -F "user_id=user456" \
  -F "service_id=service789" \
  -F "mime=application/pdf" \
  -F "channel=web"
```

### 向量嵌入生成
```bash
curl -X POST http://localhost:9001/v1/embedding/generate \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "tenant123",
    "service_id": "service789",
    "texts": ["這是一段測試文字", "這是另一段文字"]
  }'
```

## 注意事項

1. **文件大小限制**: 上傳文件需在 20MB-50MB 範圍內
2. **Token 限制**: 單次請求最大 token 數為 500,000
3. **併發限制**: 系統採用連接池管理，建議控制併發請求數量
4. **快取策略**: AI 模型實例會被快取以提高性能
5. **錯誤重試**: 系統內建重試機制，建議客戶端也實現適當的重試邏輯

---

**文檔版本**: v1.0  
**最後更新**: 2025-01-09  
**維護者**: BrainHub 開發團隊
