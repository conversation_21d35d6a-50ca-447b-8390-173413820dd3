# MCP (Model Context Protocol) 集成指南

## 概述

本文檔介紹如何在 brainHub 項目中集成和使用 MCP (Model Context Protocol) 功能。MCP 允許 AI 模型通過標準化協議與外部工具和服務進行交互。

## 架構概覽

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   LLM Router    │    │  MCP Tool Mgr   │    │  MCP Clients    │
│                 │───▶│                 │───▶│                 │
│ - 配置管理      │    │ - 工具發現      │    │ - STDIO         │
│ - 路由選擇      │    │ - 工具調用      │    │ - HTTP          │
└─────────────────┘    │ - 結果處理      │    │ - SSE           │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   MCP Servers   │
                       │                 │
                       │ - 文件系統      │
                       │ - Web API       │
                       │ - 數據庫        │
                       │ - 自定義工具    │
                       └─────────────────┘
```

## 功能特性

### 1. 多種客戶端類型支持
- **STDIO**: 本地命令行工具和腳本
- **HTTP**: Web 服務和 REST API
- **SSE**: 實時數據流和事件驅動服務

### 2. 統一工具管理
- 自動工具發現和註冊
- 工具參數驗證
- 錯誤處理和重試機制
- 工具調用緩存

### 3. 配置靈活性
- 全局配置和租戶特定配置
- 環境變量支持
- 動態配置更新
- 安全配置管理

## 快速開始

### 1. 基本配置

在 `config.yaml` 中添加 MCP 配置：

```yaml
mcp:
  enabled: true
  servers:
    - name: "filesystem"
      type: "stdio"
      description: "文件系統操作"
      command: "mcp-server-filesystem"
      args: ["--root", "/workspace"]
      timeout: "30s"
      retry_count: 3
  
  global:
    enabled: true
    default_timeout: "30s"
    max_concurrent_calls: 10
    cache_ttl: "5m"
    log_level: "info"
```

### 2. 啟用 MCP 功能

MCP 功能會在 LLM 初始化時自動啟用。確保在 LLM 配置中包含 MCP 配置：

```go
config := &llm.LLMsConfig{
    MCPConfig: &llm.MCPConfig{
        Enabled: true,
        // ... 其他配置
    },
}
```

### 3. 使用工具調用

當 AI 模型需要使用工具時，會自動通過 MCP 協議調用相應的服務：

```go
// AI 模型會自動識別並調用可用的工具
message := &llm.Message{
    ContentType: consts.ContentTypeText,
    Content:     "請幫我列出當前目錄下的文件",
}

response, err := llmInstance.Chat(ctx, message)
```

## 配置詳解

### 服務器配置

#### STDIO 服務器
```yaml
- name: "my-tool"
  type: "stdio"
  description: "我的工具描述"
  command: "/path/to/tool"
  args: ["--option", "value"]
  env:
    - "VAR=value"
  timeout: "30s"
  retry_count: 3
```

#### HTTP 服務器
```yaml
- name: "web-api"
  type: "http"
  description: "Web API 服務"
  url: "https://api.example.com/mcp"
  headers:
    Authorization: "Bearer token"
    Content-Type: "application/json"
  timeout: "60s"
  retry_count: 2
```

#### SSE 服務器
```yaml
- name: "realtime"
  type: "sse"
  description: "實時數據服務"
  url: "https://events.example.com/stream"
  headers:
    Accept: "text/event-stream"
  timeout: "120s"
  retry_count: 1
```

### 全局配置

```yaml
global:
  enabled: true                    # 是否啟用 MCP
  default_timeout: "30s"          # 默認超時時間
  max_concurrent_calls: 10        # 最大並發調用數
  cache_ttl: "5m"                 # 緩存生存時間
  log_level: "info"               # 日誌級別
```

## 開發指南

### 1. 創建自定義 MCP 服務器

```python
# 示例：Python MCP 服務器
from mcp import Server, Tool

server = Server("my-custom-server")

@server.tool("calculate")
def calculate(expression: str) -> str:
    """計算數學表達式"""
    try:
        result = eval(expression)
        return f"結果: {result}"
    except Exception as e:
        return f"錯誤: {e}"

if __name__ == "__main__":
    server.run()
```

### 2. 工具參數定義

```json
{
  "name": "search_files",
  "description": "搜索文件",
  "input_schema": {
    "type": "object",
    "properties": {
      "pattern": {
        "type": "string",
        "description": "搜索模式"
      },
      "directory": {
        "type": "string",
        "description": "搜索目錄",
        "default": "."
      }
    },
    "required": ["pattern"]
  }
}
```

### 3. 錯誤處理

```go
// 工具調用結果處理
result, err := toolManager.CallTool(ctx, "tool.name", args)
if err != nil {
    log.Errorf("Tool call failed: %v", err)
    return
}

if !result.Success {
    log.Errorf("Tool execution failed: %s", result.Error)
    return
}

log.Infof("Tool result: %s", result.Content)
```

## 最佳實踐

### 1. 安全考慮
- 使用環境變量存儲敏感信息
- 限制工具訪問權限
- 驗證工具輸入參數
- 設置合理的超時時間

### 2. 性能優化
- 啟用工具調用緩存
- 設置適當的並發限制
- 監控工具調用性能
- 使用連接池

### 3. 監控和日誌
- 記錄工具調用統計
- 監控錯誤率和響應時間
- 設置告警閾值
- 定期檢查工具可用性

### 4. 配置管理
- 使用配置版本控制
- 分環境管理配置
- 支持動態配置更新
- 備份重要配置

## 故障排除

### 常見問題

1. **工具調用超時**
   - 檢查網絡連接
   - 增加超時時間
   - 檢查服務器負載

2. **工具不可用**
   - 驗證服務器配置
   - 檢查命令路徑
   - 確認權限設置

3. **參數驗證失敗**
   - 檢查參數格式
   - 驗證必需參數
   - 確認參數類型

### 調試技巧

1. **啟用調試日誌**
   ```yaml
   global:
     log_level: "debug"
   ```

2. **檢查工具狀態**
   ```go
   health := toolManager.HealthCheck(ctx)
   fmt.Printf("Health: %+v\n", health)
   ```

3. **查看統計信息**
   ```go
   stats := toolManager.GetStats(ctx)
   fmt.Printf("Stats: %+v\n", stats)
   ```

## API 參考

### 主要接口

```go
// MCP 工具管理器接口
type MCPToolManager interface {
    Initialize(ctx context.Context) error
    GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error)
    CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error)
    ListAvailableTools(ctx context.Context) ([]ToolInfo, error)
    HealthCheck(ctx context.Context) map[string]interface{}
    Close() error
}

// 工具調用結果
type ToolResult struct {
    Success bool   `json:"success"`
    Content string `json:"content"`
    Error   string `json:"error,omitempty"`
}
```

## 部署指南

### 1. 環境準備

#### 系統要求
- Go 1.19+
- 足夠的內存和 CPU 資源
- 網絡連接（用於 HTTP/SSE 客戶端）

#### 依賴安裝
```bash
# 安裝 MCP 相關依賴
go mod download
go mod tidy

# 安裝 MCP 服務器（示例）
pip install mcp-server-filesystem
npm install @mcp/server-web-search
```

### 2. 配置部署

#### 生產環境配置
```yaml
# config/production.yaml
mcp:
  enabled: true
  servers:
    - name: "prod-filesystem"
      type: "stdio"
      command: "/opt/mcp/bin/filesystem-server"
      args: ["--root", "/data", "--readonly"]
      timeout: "60s"
      retry_count: 5

  global:
    default_timeout: "60s"
    max_concurrent_calls: 20
    cache_ttl: "10m"
    log_level: "info"
```

#### 環境變量設置
```bash
export MCP_API_TOKEN="your-secure-token"
export MCP_LOG_LEVEL="info"
export MCP_MAX_MEMORY="512MB"
```

### 3. 監控和維護

#### 健康檢查端點
```
GET /health/mcp
```

#### 指標監控
```
GET /metrics
```

#### 日誌配置
```yaml
logging:
  level: "info"
  format: "json"
  output: "/var/log/brainhub/mcp.log"
```

## 更新日誌

### v1.0.0
- 初始 MCP 集成
- 支持 STDIO、HTTP、SSE 客戶端
- 基本工具管理功能
- 配置管理和驗證

### 未來計劃
- 工具調用分析和優化
- 更多客戶端類型支持
- 高級緩存策略
- 工具組合和鏈式調用
