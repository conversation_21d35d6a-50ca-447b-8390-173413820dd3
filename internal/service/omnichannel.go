// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/model/omnichannel"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type (
	IOmniChannel interface {
		CreateAnswer(ctx context.Context, in g.MapStrStr) (answer []*omnichannel.Root, err error)
	}
)

var (
	localOmniChannel IOmniChannel
)

func OmniChannel() IOmniChannel {
	if localOmniChannel == nil {
		panic("implement not found for interface IOmniChannel, forgot register?")
	}
	return localOmniChannel
}

func RegisterOmniChannel(i IOmniChannel) {
	localOmniChannel = i
}
