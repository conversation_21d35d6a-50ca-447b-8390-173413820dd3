// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/consts"
	"context"
)

type (
	IMessageQ interface {
		// Send 發送消息到 RabbitMQ
		Send(ctx context.Context, routeKey string, action string, data []byte) (err error)

		// RegisterHandler 註冊消息處理器
		// routeKeyPrefix: 路由鍵前綴（如 "mariadb."、"weaviate."）
		// handler: 消息處理函數
		RegisterHandler(routeKeyPrefix string, handler consts.OnMessage)

		// InitReceive 初始化消息接收服務
		InitReceive(ctx context.Context) error

		// IsHealthy 檢查服務健康狀態
		IsHealthy() bool

		// Close 關閉服務
		Close() error
	}
)

var (
	localMessageQ IMessageQ
)

func MessageQ() IMessageQ {
	if localMessageQ == nil {
		panic("implement not found for interface IMessageQ, forgot register?")
	}
	return localMessageQ
}

func RegisterMessageQ(i IMessageQ) {
	localMessageQ = i
}
