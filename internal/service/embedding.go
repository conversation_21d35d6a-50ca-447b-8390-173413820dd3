// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"brainHub/internal/embeddings"
	"brainHub/internal/model/embedding"
	"context"
)

type (
	IEmbeddingService interface {
		// GenerateEmbeddings 生成文本向量
		// 參數：
		//   - ctx: 上下文
		//   - req: 嵌入請求
		// 返回：
		//   - *embedding.EmbeddingResponse: 嵌入響應
		//   - error: 處理錯誤
		GenerateEmbeddings(ctx context.Context, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)

		// GenerateEmbeddingsWithContext 生成文本向量（帶租戶和服務上下文）
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		//   - req: 嵌入請求
		// 返回：
		//   - *embedding.EmbeddingResponse: 嵌入響應
		//   - error: 處理錯誤
		GenerateEmbeddingsWithContext(ctx context.Context, tenantID, serviceID string, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error)

		// GenerateSingleEmbedding 生成單個文本向量
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		//   - text: 文本內容
		// 返回：
		//   - []float32: 生成的向量
		//   - error: 處理錯誤
		GenerateSingleEmbedding(ctx context.Context, tenantID, serviceID, text string) ([]float32, error)

		// GetEmbeddingProvider 獲取 embedding 提供商實例
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		// 返回：
		//   - embeddings.IEmbeddings: embedding 實例
		//   - error: 獲取錯誤
		GetEmbeddingProvider(ctx context.Context, tenantID, serviceID string) (embeddings.IEmbeddings, error)

		// GetSupportedProviders 獲取支持的提供商列表
		// 返回：
		//   - []string: 支持的提供商列表
		GetSupportedProviders() []string

		// ValidateEmbeddingConfig 驗證 embedding 配置
		// 參數：
		//   - config: embedding 配置
		// 返回：
		//   - error: 驗證錯誤
		ValidateEmbeddingConfig(config *embedding.EmbeddingConfig) error

		// GetEmbeddingMetrics 獲取 embedding 性能指標
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		// 返回：
		//   - *embedding.EmbeddingMetrics: 性能指標
		//   - error: 獲取錯誤
		GetEmbeddingMetrics(ctx context.Context, tenantID, serviceID string) (*embedding.EmbeddingMetrics, error)

		// CheckEmbeddingHealth 檢查 embedding 服務健康狀態
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		// 返回：
		//   - bool: 是否健康
		//   - error: 檢查錯誤
		CheckEmbeddingHealth(ctx context.Context, tenantID, serviceID string) (bool, error)

		// ReleaseEmbeddingProvider 釋放 embedding 提供商資源
		// 參數：
		//   - ctx: 上下文
		//   - tenantID: 租戶ID
		//   - serviceID: 服務ID
		// 返回：
		//   - error: 釋放錯誤
		ReleaseEmbeddingProvider(ctx context.Context, tenantID, serviceID string) error
	}
)

var (
	localEmbeddingService IEmbeddingService
)

func EmbeddingService() IEmbeddingService {
	if localEmbeddingService == nil {
		panic("implement not found for interface IEmbeddingService, forgot register?")
	}
	return localEmbeddingService
}

func RegisterEmbeddingService(i IEmbeddingService) {
	localEmbeddingService = i
}
