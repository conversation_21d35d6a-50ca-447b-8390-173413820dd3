package consts

const (
	UriDSHGetContents       = `/v1/getContents`
	UriDownloadFile         = `/v1/attachments/download`
	SchemaDSH               = "dsh"
	TableTenantLLM          = "tenant_llm"
	TableLLMParams          = "llm_params"
	TableChatMessage        = "chat_message_%s"
	TableTenantPrompt       = "tenant_prompt"
	TableChatMessagePattern = "chat_message_%s"
)
const (
	RedisKeyTenantLLM     = "tenant_llm:%s" // tenant id
	RedisKeyPrompt        = "tenant_prompt:%s"
	RedisLLMParams        = "llm_params:%s"            // llm name
	RedisChatMessages     = "chat_message:%s:%s:%s:%s" // tenant id service id user id channel
	RouteKeyBrainHub      = "mariadb.brainHub"
	TableResourcesPattern = "%s_resources"

	// MessageQ 路由鍵常量
	RouteKeyPayloadChanged = "payloadChanged" // LLM 配置變更通知路由鍵
)
