package brain

import (
	"brainHub/api/brain/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/internal/service"
	"brainHub/utility"
	"context"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GenerateContent 統一的內容生成 API 端點
func (c *ControllerV1) GenerateContent(ctx context.Context, req *v1.GenerateContentReq) (res *v1.GenerateContentRes, err error) {
	res = &v1.GenerateContentRes{}
	r := ghttp.RequestFromCtx(ctx)

	// 錯誤處理函數
	fnProcessError := func(e error) {
		if e != nil {
			code := gerror.Code(e)
			res.Code = code.Code()
			if code == gcode.CodeNil {
				res.Message = e.Error()
			} else {
				res.Message = code.Message()
			}
		}
	}

	// 參數驗證
	if req.TenantID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "tenant id cannot be empty"))
		r.Response.WriteJsonExit(res)
		return
	}

	if req.ServiceID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "the service id cannot be empty"))
		r.Response.WriteJsonExit(res)
		return
	}

	if req.Channel == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "channel id cannot be empty"))
		r.Response.WriteJsonExit(res)
		return
	}

	if req.Prompt == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "the prompt word cannot be empty"))
		r.Response.WriteJsonExit(res)
		return
	}

	// 選擇 AI 模型
	ai, err := service.AiRouter().Select(ctx, &model.AiSelectorInput{
		TenantID:  req.TenantID,
		ServiceID: req.ServiceID,
		UserID:    "", // GenerateContent 不需要 UserID
		Channel:   req.Channel,
	})

	if err != nil {
		fnProcessError(err)
		r.Response.WriteJsonExit(res)
		return
	}

	// 構建生成請求
	generateRequest := &llm.GenerateContentRequest{
		Prompt:            req.Prompt,
		MaxContinuations:  3,     // 預設最大續寫次數
		TotalTokenBudget:  8000,  // 預設 Token 預算
		IncludeThinking:   false, // 預設不包含思考過程
		SystemInstruction: "",    // 可以根據需要添加系統指令
	}

	// 調用統一的 GenerateContent 接口
	generateResponse, err := ai.GenerateContent(ctx, generateRequest)
	if err != nil {
		fnProcessError(err)
		r.Response.WriteJsonExit(res)
		return
	}

	// 構建響應數據
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Data = &v1.GenerateContentResData{
		LLMName:           generateResponse.LLMName,
		OutputContent:     generateResponse.OutputContent,
		InputTokens:       generateResponse.InputTokens,
		OutputTokens:      generateResponse.OutputTokens,
		TotalTokens:       generateResponse.TotalTokens,
		ContinuationCount: generateResponse.ContinuationCount,
		IsComplete:        generateResponse.IsComplete,
		GenerationTime:    generateResponse.GenerationTime,
		ThinkingProcess:   generateResponse.ThinkingProcess,
		SafetyWarnings:    generateResponse.SafetyWarnings,
		FinishReason:      generateResponse.FinishReason,
	}

	// 記錄生成日誌
	utility.LogGenerateContent(ctx, req.TenantID, req.ServiceID, req.Channel, generateResponse)

	r.Response.WriteJsonExit(res)
	return
}
