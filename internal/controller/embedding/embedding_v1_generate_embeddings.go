package embedding

import (
	"brainHub/api/embedding/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model/embedding"
	"brainHub/internal/service"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GenerateEmbeddings 生成文本向量
func (c *ControllerV1) GenerateEmbeddings(ctx context.Context, req *v1.GenerateEmbeddingsReq) (res *v1.GenerateEmbeddingsRes, err error) {
	res = &v1.GenerateEmbeddingsRes{}
	r := ghttp.RequestFromCtx(ctx)
	startTime := time.Now()

	// 錯誤處理函數
	fnProcessError := func(e error) {
		if e != nil {
			code := gerror.Code(e)
			res.Code = code.Code()
			if code == gcode.CodeNil {
				res.Message = e.Error()
			} else {
				res.Message = code.Message()
			}

			// Log error
			g.Log().Cat(consts.CatalogEmbedding).Errorf(ctx,
				"GenerateEmbeddings failed: tenant_id=%s, service_id=%s, texts_count=%d, error=%v",
				req.TenantID, req.ServiceID, len(req.Texts), e)
		}
	}

	// Parameter validation
	if req.TenantID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "tenant_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	if req.ServiceID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "service_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	if len(req.Texts) == 0 {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "texts list cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Check text count limit
	if len(req.Texts) > consts.EmbeddingDefaultBatchSize {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter,
			fmt.Sprintf("text count exceeds limit, maximum %d texts supported", consts.EmbeddingDefaultBatchSize)))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Check individual text length limit
	for i, text := range req.Texts {
		if len(text) > consts.EmbeddingMaxTextLength {
			fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter,
				fmt.Sprintf("text %d length exceeds limit, maximum %d characters supported", i+1, consts.EmbeddingMaxTextLength)))
			if r != nil {
				r.Response.WriteJsonExit(res)
			}
			return
		}
		if text == "" {
			fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter,
				fmt.Sprintf("text %d cannot be empty", i+1)))
			if r != nil {
				r.Response.WriteJsonExit(res)
			}
			return
		}
	}

	// Log request
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GenerateEmbeddings request: tenant_id=%s, service_id=%s, texts_count=%d, model=%s",
		req.TenantID, req.ServiceID, len(req.Texts), req.Model)

	// Build embedding request
	embeddingRequest := &embedding.EmbeddingRequest{
		Texts: req.Texts,
		Model: req.Model,
	}

	// Call embedding service with tenant and service context
	embeddingResponse, err := service.EmbeddingService().GenerateEmbeddingsWithContext(ctx, req.TenantID, req.ServiceID, embeddingRequest)
	if err != nil {
		fnProcessError(err)
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Calculate processing time
	processingTime := time.Since(startTime).Milliseconds()

	// Build response data
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Data = &v1.GenerateEmbeddingsResData{
		Embeddings:  embeddingResponse.Embeddings,
		Model:       embeddingResponse.Model,
		Provider:    embeddingResponse.Provider,
		Usage:       embeddingResponse.Usage,
		ProcessTime: processingTime,
		Dimensions:  len(embeddingResponse.Embeddings[0]), // Assume at least one vector
	}

	// Log success
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GenerateEmbeddings success: tenant_id=%s, service_id=%s, texts_count=%d, model=%s, process_time=%dms, total_tokens=%d",
		req.TenantID, req.ServiceID, len(req.Texts), embeddingResponse.Model, processingTime, embeddingResponse.Usage.TotalTokens)

	if r != nil {
		r.Response.WriteJsonExit(res)
	}
	return
}
