package embedding

import (
	"brainHub/api/embedding/v1"
	"brainHub/internal/consts"
	"brainHub/internal/service"
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// GetEmbeddingHealth 檢查 embedding 服務健康狀態
func (c *ControllerV1) GetEmbeddingHealth(ctx context.Context, req *v1.GetEmbeddingHealthReq) (res *v1.GetEmbeddingHealthRes, err error) {
	res = &v1.GetEmbeddingHealthRes{}
	r := ghttp.RequestFromCtx(ctx)
	checkTime := time.Now().Unix()

	// 錯誤處理函數
	fnProcessError := func(e error) {
		if e != nil {
			code := gerror.Code(e)
			res.Code = code.Code()
			if code == gcode.CodeNil {
				res.Message = e.Error()
			} else {
				res.Message = code.Message()
			}

			// Log error
			g.Log().Cat(consts.CatalogEmbedding).Errorf(ctx,
				"GetEmbeddingHealth failed: tenant_id=%s, service_id=%s, error=%v",
				req.TenantID, req.ServiceID, e)
		}
	}

	// Parameter validation
	if req.TenantID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "tenant_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	if req.ServiceID == "" {
		fnProcessError(gerror.NewCode(gcode.CodeInvalidParameter, "service_id cannot be empty"))
		if r != nil {
			r.Response.WriteJsonExit(res)
		}
		return
	}

	// Log request
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GetEmbeddingHealth request: tenant_id=%s, service_id=%s",
		req.TenantID, req.ServiceID)

	// Check embedding service health
	healthy, err := service.EmbeddingService().CheckEmbeddingHealth(ctx, req.TenantID, req.ServiceID)
	if err != nil {
		// Even if health check fails, we return result but mark as unhealthy
		g.Log().Cat(consts.CatalogEmbedding).Warningf(ctx,
			"Embedding health check failed: tenant_id=%s, service_id=%s, error=%v",
			req.TenantID, req.ServiceID, err)
		healthy = false
	}

	// Get embedding provider information
	embeddingProvider, providerErr := service.EmbeddingService().GetEmbeddingProvider(ctx, req.TenantID, req.ServiceID)

	var provider, model string
	if providerErr == nil && embeddingProvider != nil {
		provider = embeddingProvider.GetProvider()
		model = embeddingProvider.GetModelName()
	} else {
		provider = "unknown"
		model = "unknown"
		g.Log().Cat(consts.CatalogEmbedding).Warningf(ctx,
			"Failed to get embedding provider info: tenant_id=%s, service_id=%s, error=%v",
			req.TenantID, req.ServiceID, providerErr)
	}

	// Build response data
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Data = &v1.GetEmbeddingHealthResData{
		Healthy:   healthy,
		Provider:  provider,
		Model:     model,
		CheckTime: checkTime,
	}

	// Log result
	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"GetEmbeddingHealth result: tenant_id=%s, service_id=%s, healthy=%t, provider=%s, model=%s",
		req.TenantID, req.ServiceID, healthy, provider, model)

	if r != nil {
		r.Response.WriteJsonExit(res)
	}
	return
}
