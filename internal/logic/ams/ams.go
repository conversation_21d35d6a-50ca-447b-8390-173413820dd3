package ams

import (
	"brainHub/boot"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/service"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
)

func init() {
	service.RegisterAMS(New())
}

type sAMS struct {
	client *gclient.Client
}

func New() service.IAMS {
	boot.WaitReady()
	s := &sAMS{
		client: g.Client(),
	}
	s.client = g.Client().Discovery(gsvc.GetRegistry())

	return s
}

func (s *sAMS) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogAMS)
}
func (s *sAMS) downloadFile(ctx context.Context, filePath string, tenantID, serviceID, userID string) (filePathName string, err error) {
	vAmsServiceName, _ := g.Cfg().Get(ctx, "system.asset_management.name", "ams.svc")
	vScheme, _ := g.Cfg().Get(ctx, "system.asset_management.scheme", "http")
	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vAmsServiceName.String(), consts.UriDownloadFile)
	s.logger().Debugf(ctx, "send to url=%v ,file path =%v", url, filePath)
	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).Post(ctx, url, g.Map{"file_path": filePath})
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)

	bufContent := response.ReadAll()

	if gjson.Valid(string(bufContent)) {
		s.logger().Debugf(ctx, "response=%v", gjson.New(string(bufContent)).MustToJsonString())
	} else {
		s.logger().Noticef(ctx, "the file size : %v", len(bufContent))
		vPath, _ := g.Cfg().Get(ctx, "system.asset_path")

		if g.IsEmpty(userID) {
			filePathName = gfile.Join(vPath.String(), tenantID, serviceID, gfile.Basename(filePath))
		} else {
			filePathName = gfile.Join(vPath.String(), tenantID, serviceID, userID, gfile.Basename(filePath))
		}
		s.logger().Noticef(ctx, "the file save path =%v", filePathName)

		if err = gfile.PutBytes(filePathName, bufContent); err != nil {
			s.logger().Error(ctx, err)
		}
	}

	return
}

func (s *sAMS) fullInWithFiles(ctx context.Context, tenantID, serviceID, userID string, asset *model.Asset, files []*model.FileContent) (err error) {
	if asset == nil || files == nil {
		err = gerror.New("asset or files is nil")
		s.logger().Warning(ctx, err)
		return
	}
	for _, file := range files {
		downloadFile := ""
		if !g.IsEmpty(file.AccessPath) {
			downloadFile = file.AccessPath
		} else if !g.IsEmpty(file.AccessUrl) {
			downloadFile = file.AccessUrl
		}
		if g.IsEmpty(downloadFile) {
			s.logger().Debugf(ctx, "file %v path is emtpy", file.FileName)
			continue
		}
		if tmpFilePath, e := s.downloadFile(ctx, downloadFile, tenantID, serviceID, userID); e != nil {
			s.logger().Error(ctx, e)
		} else {
			if gfile.Exists(tmpFilePath) && gfile.Size(tmpFilePath) > 0 {
				asset.Files = append(asset.Files, tmpFilePath)
			}
		}
	}

	return
}

func (s *sAMS) fullInWithWebPage(ctx context.Context, tenantID, serviceID, userID string, asset *model.Asset, webSites []*model.WebSiteURLContent) (err error) {
	if asset == nil || webSites == nil {
		err = gerror.New("asset or webSites is nil")
		s.logger().Warning(ctx, err)
		return
	}
	for _, site := range webSites {
		for _, fileContent := range site.PageToFileContent {
			downloadFile := ""
			if !g.IsEmpty(fileContent.AccessPath) {
				downloadFile = fileContent.AccessPath
			} else if !g.IsEmpty(fileContent.AccessUrl) {
				downloadFile = fileContent.AccessUrl
			}
			if g.IsEmpty(downloadFile) {
				s.logger().Debugf(ctx, "file %v path is emtpy", fileContent.FileName)
				continue
			}
			if tmpFilePath, e := s.downloadFile(ctx, downloadFile, tenantID, serviceID, userID); e != nil {
				s.logger().Error(ctx, e)
			} else {
				if gfile.Exists(tmpFilePath) && gfile.Size(tmpFilePath) > 0 {
					asset.WebPageFiles = append(asset.WebPageFiles, tmpFilePath)
				}
			}

		}
	}
	return
}
func (s *sAMS) fullInWithYoutubeLink(ctx context.Context, asset *model.Asset, youtubeLinks []*model.YoutubeContent) (err error) {
	for _, link := range youtubeLinks {
		asset.YoutubeLink = append(asset.YoutubeLink, link.YoutubeLink)
	}
	return
}
func (s *sAMS) fullInWithPlainText(ctx context.Context, asset *model.Asset, plainTexts []*model.PlainTextContent) (err error) {
	for _, text := range plainTexts {
		asset.PlainText = append(asset.PlainText, text.PlainText)
	}
	return
}

func (s *sAMS) GetResources(ctx context.Context, tenantID string, serviceID string) (asset *model.Asset, err error) {
	s.logger().Debugf(ctx, "get resources from ams with tenantID [%v] and serviceID [%v]", tenantID, serviceID)
	resourcesRecord, err := service.DSH().GetResources(ctx, tenantID, serviceID)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	asset = &model.Asset{
		YoutubeLink:  make([]string, 0),
		PlainText:    make([]string, 0),
		WebPageFiles: make([]string, 0),
		Files:        make([]string, 0),
	}

	for _, record := range resourcesRecord {
		_ = s.fullInWithFiles(ctx, tenantID, serviceID, record.UploadUserID, asset, record.UploadFiles)
		_ = s.fullInWithWebPage(ctx, tenantID, serviceID, record.UploadUserID, asset, record.URLContents)
		_ = s.fullInWithYoutubeLink(ctx, asset, record.YoutubeContents)
		_ = s.fullInWithPlainText(ctx, asset, record.PlainTextContents)
	}

	return

}
