// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package embedding

import (
	"brainHub/internal/consts"
	"brainHub/internal/embeddings"
	"brainHub/internal/model"
	"brainHub/internal/model/embedding"
	"brainHub/internal/service"
	"context"
	"sync"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

type (
	sEmbedding struct {
		// 緩存 embedding 實例，避免重複創建
		providerCache *gcache.Cache
		cacheMutex    sync.RWMutex
	}
)

func init() {
	service.RegisterEmbeddingService(New())
}

// New 創建新的 embedding 服務實例
func New() service.IEmbeddingService {
	return &sEmbedding{
		providerCache: gcache.New(),
	}
}

// GenerateEmbeddings 生成文本向量（使用默認配置）
func (s *sEmbedding) GenerateEmbeddings(ctx context.Context, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error) {
	if req == nil {
		return nil, gerror.New("embedding request cannot be nil")
	}

	if len(req.Texts) == 0 {
		return nil, gerror.New("texts cannot be empty")
	}

	// Use default tenant and service for backward compatibility
	return s.GenerateEmbeddingsWithContext(ctx, "default", "default", req)
}

// GenerateEmbeddingsWithContext 生成文本向量（帶租戶和服務上下文）
func (s *sEmbedding) GenerateEmbeddingsWithContext(ctx context.Context, tenantID, serviceID string, req *embedding.EmbeddingRequest) (*embedding.EmbeddingResponse, error) {
	if req == nil {
		return nil, gerror.New("embedding request cannot be nil")
	}

	if len(req.Texts) == 0 {
		return nil, gerror.New("texts cannot be empty")
	}

	if tenantID == "" || serviceID == "" {
		return nil, gerror.New("tenant_id and service_id cannot be empty")
	}

	// Get embedding provider for specific tenant and service
	embeddingProvider, err := s.GetEmbeddingProvider(ctx, tenantID, serviceID)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get embedding provider")
	}

	// Generate embeddings with usage statistics
	response, err := embeddingProvider.GenerateEmbeddingsWithUsage(ctx, req)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to generate embeddings")
	}

	return response, nil
}

// GenerateSingleEmbedding 生成單個文本向量
func (s *sEmbedding) GenerateSingleEmbedding(ctx context.Context, tenantID, serviceID, text string) ([]float32, error) {
	if text == "" {
		return nil, gerror.New("text cannot be empty")
	}

	// 獲取 embedding 提供商
	embeddingProvider, err := s.GetEmbeddingProvider(ctx, tenantID, serviceID)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get embedding provider")
	}

	// 生成單個向量
	vector, err := embeddingProvider.GenerateSingleEmbedding(ctx, text)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to generate single embedding")
	}

	return vector, nil
}

// GetEmbeddingProvider 獲取 embedding 提供商實例
func (s *sEmbedding) GetEmbeddingProvider(ctx context.Context, tenantID, serviceID string) (embeddings.IEmbeddings, error) {
	if tenantID == "" || serviceID == "" {
		return nil, gerror.New("tenant_id and service_id cannot be empty")
	}

	// 生成緩存鍵
	cacheKey := s.generateCacheKey(tenantID, serviceID)

	// 嘗試從緩存獲取
	s.cacheMutex.RLock()
	if cached := s.providerCache.MustGet(ctx, cacheKey); cached != nil {
		s.cacheMutex.RUnlock()
		if provider, ok := cached.Val().(embeddings.IEmbeddings); ok {
			return provider, nil
		}
	}
	s.cacheMutex.RUnlock()

	// 緩存未命中，創建新的提供商實例
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	// 雙重檢查
	if cached := s.providerCache.MustGet(ctx, cacheKey); cached != nil {
		if provider, ok := cached.Val().(embeddings.IEmbeddings); ok {
			return provider, nil
		}
	}

	// Get LLM parameters from configuration management system
	llmParams, err := s.getLLMParams(ctx, tenantID, serviceID)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get LLM params")
	}

	// Create embedding instance from LLMParams
	embeddingProvider, err := embeddings.CreateEmbeddingFromLLMParams(llmParams)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to create embedding provider")
	}

	// Cache instance (30 minutes expiration)
	s.providerCache.Set(ctx, cacheKey, embeddingProvider, 30*60*1000) // 30 minutes

	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"Created and cached embedding provider: tenant_id=%s, service_id=%s, provider=%s",
		tenantID, serviceID, embeddingProvider.GetProvider())

	return embeddingProvider, nil
}

// GetSupportedProviders 獲取支持的提供商列表
func (s *sEmbedding) GetSupportedProviders() []string {
	factory := embeddings.GetFactory()
	return factory.GetSupportedProviders()
}

// ValidateEmbeddingConfig 驗證 embedding 配置
func (s *sEmbedding) ValidateEmbeddingConfig(config *embedding.EmbeddingConfig) error {
	return embeddings.ValidateConfig(config)
}

// GetEmbeddingMetrics 獲取 embedding 性能指標
func (s *sEmbedding) GetEmbeddingMetrics(ctx context.Context, tenantID, serviceID string) (*embedding.EmbeddingMetrics, error) {
	embeddingProvider, err := s.GetEmbeddingProvider(ctx, tenantID, serviceID)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get embedding provider")
	}

	metrics := embeddingProvider.GetMetrics()
	return metrics, nil
}

// CheckEmbeddingHealth 檢查 embedding 服務健康狀態
func (s *sEmbedding) CheckEmbeddingHealth(ctx context.Context, tenantID, serviceID string) (bool, error) {
	embeddingProvider, err := s.GetEmbeddingProvider(ctx, tenantID, serviceID)
	if err != nil {
		return false, gerror.Wrap(err, "failed to get embedding provider")
	}

	healthy, err := embeddingProvider.IsHealthy(ctx)
	if err != nil {
		return false, gerror.Wrap(err, "health check failed")
	}

	return healthy, nil
}

// ReleaseEmbeddingProvider 釋放 embedding 提供商資源
func (s *sEmbedding) ReleaseEmbeddingProvider(ctx context.Context, tenantID, serviceID string) error {
	cacheKey := s.generateCacheKey(tenantID, serviceID)

	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()

	// Get instance from cache
	if cached := s.providerCache.MustGet(ctx, cacheKey); cached != nil {
		if provider, ok := cached.Val().(embeddings.IEmbeddings); ok {
			// Release resources
			provider.Release(ctx)

			g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
				"Released embedding provider: tenant_id=%s, service_id=%s",
				tenantID, serviceID)
		}
	}

	// Remove from cache
	s.providerCache.Remove(ctx, cacheKey)

	return nil
}

// generateCacheKey 生成緩存鍵
func (s *sEmbedding) generateCacheKey(tenantID, serviceID string) string {
	return "embedding_provider:" + tenantID + ":" + serviceID
}

// getLLMParams 獲取 LLM 參數（從配置管理系統獲取）
// 參考 internal/logic/llmRouter/router.go 中的 getLLMConfig 方法實現
func (s *sEmbedding) getLLMParams(ctx context.Context, tenantID, serviceID string) (*model.LLMParams, error) {
	if tenantID == "" || serviceID == "" {
		return nil, gerror.New("tenant_id and service_id cannot be empty")
	}

	// Get LLM configuration from DSH service
	// Following the same pattern as AI Router
	var dshService service.IDSH

	// Try to get DSH service, handle gracefully if not available (e.g., in tests)
	func() {
		defer func() {
			if r := recover(); r != nil {
				g.Log().Cat(consts.CatalogEmbedding).Warningf(ctx,
					"DSH service not available (likely in test environment): %v", r)
				dshService = nil
			}
		}()
		dshService = service.DSH()
	}()

	if dshService == nil {
		return nil, gerror.New("DSH service not available")
	}

	// Get LLM parameters for the specific tenant (DSH service only uses tenantID)
	llmParams, err := dshService.GetLLMParams(ctx, tenantID)
	if err != nil {
		g.Log().Cat(consts.CatalogEmbedding).Errorf(ctx,
			"Failed to get LLM params from DSH: tenant_id=%s, service_id=%s, error=%v",
			tenantID, serviceID, err)
		return nil, gerror.Wrap(err, "failed to get LLM params from DSH service")
	}

	if llmParams == nil {
		return nil, gerror.Newf("no LLM params found for tenant_id=%s, service_id=%s", tenantID, serviceID)
	}

	// Validate that the LLM params are suitable for embedding
	if err := embeddings.ValidateLLMParamsForEmbedding(llmParams); err != nil {
		g.Log().Cat(consts.CatalogEmbedding).Errorf(ctx,
			"LLM params validation failed for embedding: tenant_id=%s, service_id=%s, error=%v",
			tenantID, serviceID, err)
		return nil, gerror.Wrap(err, "LLM params validation failed for embedding")
	}

	g.Log().Cat(consts.CatalogEmbedding).Infof(ctx,
		"Successfully retrieved LLM params for embedding: tenant_id=%s, service_id=%s, llm_type=%s, embedding_model=%s",
		tenantID, serviceID, llmParams.LLMType, llmParams.EmbeddingModel)

	return llmParams, nil
}
