2025-07-17T10:08:32.460+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T10:08:32.460+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T10:08:32.460+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-17T10:08:32.460+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4475b1db-16b0-4c12-a7c0-3c24aba8970d)
2025-07-17T10:08:32.460+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T10:08:32.460+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T10:08:32.460+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T10:08:32.460+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4475b1db-16b0-4c12-a7c0-3c24aba8970d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T10:08:32.460+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T10:08:32.460+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T10:08:32.460+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T10:08:32.573+0800	INFO	rpc/rpc_client.go:337	config-0-4475b1db-16b0-4c12-a7c0-3c24aba8970d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752718112605_192.168.3.3_54653
2025-07-17T10:08:32.574+0800	INFO	rpc/rpc_client.go:486	config-0-4475b1db-16b0-4c12-a7c0-3c24aba8970d notify connected event to listeners , connectionId=1752718112605_192.168.3.3_54653
2025-07-17T10:08:32.574+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-17T10:08:32.585+0800	ERROR	cache/disk_cache.go:105	make dir failed, dir path ./nacos/cfg/config, err: mkdir /Users/<USER>/Source/Ai app/brainHub/internal/logic/messageQ/./nacos/cfg/config: file exists.
2025-07-17T10:34:45.682+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T10:34:45.683+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-17T10:34:45.683+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-48e02658-5bbc-42cc-ae04-f44a667f7e9f)
2025-07-17T10:34:45.683+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T10:34:45.683+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T10:34:45.683+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T10:34:45.683+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-48e02658-5bbc-42cc-ae04-f44a667f7e9f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T10:34:45.683+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T10:34:45.683+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T10:34:45.683+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T10:34:45.797+0800	INFO	rpc/rpc_client.go:337	config-0-48e02658-5bbc-42cc-ae04-f44a667f7e9f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752719685842_192.168.3.3_58133
2025-07-17T10:34:45.798+0800	INFO	rpc/rpc_client.go:486	config-0-48e02658-5bbc-42cc-ae04-f44a667f7e9f notify connected event to listeners , connectionId=1752719685842_192.168.3.3_58133
2025-07-17T10:34:45.798+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
