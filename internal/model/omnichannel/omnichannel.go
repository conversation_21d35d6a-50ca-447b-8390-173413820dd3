package omnichannel

type Root struct {
	QuickReply *QuickReply `json:"quickReply"`
	Text       string      `json:"text"`
	Type       string      `json:"type"`
}

type QuickReply struct {
	Items []*Item `json:"items"`
}

type Item struct {
	Action *Action `json:"action"`
	Type   string  `json:"type"`
}

type Action struct {
	Data        string `json:"data"`
	DisplayText string `json:"displayText"`
	Title       string `json:"title"`
	Type        string `json:"type"`
}
