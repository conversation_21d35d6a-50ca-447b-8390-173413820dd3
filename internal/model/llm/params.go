package llm

import (
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"fmt"
)

type LLMsConfig struct {
	Common    CommonConfig     `json:"common"`
	Vertex    VertexConfig     `json:"vertex"`
	AoAi      *model.LLMParams `json:"aoai"`
	MCPConfig *MCPConfig       `json:"mcp_config"`
}
type CommonConfig struct {
	MaxOutputTokens int32   `json:"max_output_tokens"`
	Temperature     float32 `json:"temperature"`
}
type VertexConfig struct {
	Region         string       `json:"region"`
	ProjectID      string       `json:"project_id"`
	CredentialFile string       `json:"credential_file"`
	LLMType        string       `json:"llm_type"`
	Gemini         GeminiConfig `json:"gemini"`
	ThirdModel     ModelConfig  `json:"third_model"`
}
type ModelConfig struct {
	Model           string  `json:"model"`
	APIVersion      string  `json:"api_version"`
	Temperature     float32 `json:"temperature"`
	MaxOutputTokens int32   `json:"max_output_tokens"`
}

type GeminiConfig struct {
	Model           string  `json:"model"`
	Temperature     float32 `json:"temperature"`
	MaxOutputTokens int32   `json:"max_output_tokens"`
	IncludeThoughts bool    `json:"include_thoughts"`
	ThinkingBudget  int32   `json:"thinking_budget"`
}

type ResponseData struct {
	Response        any    `json:"response"`
	TotalTokenCount int32  `json:"total_token_count"`
	TenantID        string `json:"tenant_id"`
	UserID          string `json:"user_id"`
	ServiceID       string `json:"service_id"`
	Channel         string `json:"channel"`
}

func (c *LLMsConfig) VertexEndPointForClaude() string {
	// https://REGION-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/REGION
	// https://REGION-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/REGION/publishers/anthropic/models/MODEL_NAME:rawPredict
	if c.Vertex.LLMType == consts.VertexAIClaude {
		return fmt.Sprintf(`https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/anthropic/models/%s:rawPredict`,
			c.Vertex.Region,
			c.Vertex.ProjectID,
			c.Vertex.Region,
			c.Vertex.ThirdModel.Model,
		)

	}

	return ""
}

func (c *LLMsConfig) VertexEndPointForGemini() string {
	// https://REGION-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/REGION/publishers/google/models/MODEL_NAME:generateContent
	if c.Vertex.LLMType == consts.VertexAIGemini {
		return fmt.Sprintf(`https://%s-aiplatform.googleapis.com/v1/projects/%s/locations/%s/publishers/google/models/%s:generateContent`,
			/* c.Vertex.Region */ "us-central1",
			c.Vertex.ProjectID,
			// c.Vertex.Region
			"us-central1",
			c.Vertex.Gemini.Model,
		)
	}

	return ""
}

type Payload struct {
	Attachments       *model.Asset
	SystemInstruction string
	History           any
}

// MCPConfig MCP 配置結構
type MCPConfig struct {
	Enabled bool              `json:"enabled"`
	Servers []MCPServerConfig `json:"servers"`
	Global  MCPGlobalConfig   `json:"global"`
}

// MCPServerConfig MCP 服務器配置
type MCPServerConfig struct {
	Name        string            `json:"name"`
	Type        string            `json:"type"` // stdio, http, sse
	Description string            `json:"description"`
	Command     string            `json:"command,omitempty"` // for stdio
	Args        []string          `json:"args,omitempty"`    // for stdio
	URL         string            `json:"url,omitempty"`     // for http/sse
	Headers     map[string]string `json:"headers,omitempty"` // for http/sse
	Env         []string          `json:"env,omitempty"`     // for stdio
	Timeout     string            `json:"timeout"`
	RetryCount  int               `json:"retry_count"`
}

// MCPGlobalConfig MCP 全局配置
type MCPGlobalConfig struct {
	Enabled            bool   `json:"enabled"`
	DefaultTimeout     string `json:"default_timeout"`
	MaxConcurrentCalls int    `json:"max_concurrent_calls"`
	CacheTTL           string `json:"cache_ttl"`
	LogLevel           string `json:"log_level"`
}
