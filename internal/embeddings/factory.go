package embeddings

import (
	"brainHub/internal/consts"
	"brainHub/internal/embeddings/providers/aoai"
	"brainHub/internal/model"
	"brainHub/internal/model/embedding"
	"context"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// DefaultFactory 默認的 embedding 工廠實現
type DefaultFactory struct{}

// NewFactory 創建新的工廠實例
func NewFactory() EmbeddingFactory {
	return &DefaultFactory{}
}

// CreateEmbedding 創建未初始化的 embedding 實例
// 注意：返回的實例需要調用 Initialize() 方法才能使用
// 建議使用 CreateEmbeddingWithConfig() 或 CreateEmbeddingFromLLMParams() 方法
func (f *DefaultFactory) CreateEmbedding(provider string) (IEmbeddings, error) {
	// 標準化提供商名稱
	provider = gstr.ToLower(gstr.Trim(provider))

	switch provider {
	case consts.EmbeddingAOAI:
		return aoai.New(), nil
	case consts.EmbeddingOpenAI:
		// 未來實現：return openai.New(), nil
		return nil, gerror.Newf("OpenAI embedding provider not implemented yet")
	case consts.EmbeddingVertexAI:
		// 未來實現：return vertex.New(), nil
		return nil, gerror.Newf("Vertex AI embedding provider not implemented yet")
	case consts.EmbeddingHuggingFace:
		// 未來實現：return huggingface.New(), nil
		return nil, gerror.Newf("Hugging Face embedding provider not implemented yet")
	case consts.EmbeddingLocal:
		// 未來實現：return local.New(), nil
		return nil, gerror.Newf("Local embedding provider not implemented yet")
	default:
		return nil, gerror.Newf("unsupported embedding provider: %s", provider)
	}
}

// CreateEmbeddingWithConfig 創建並初始化 embedding 實例
func (f *DefaultFactory) CreateEmbeddingWithConfig(config *embedding.EmbeddingConfig) (IEmbeddings, error) {
	if config == nil {
		return nil, gerror.New("config cannot be nil")
	}

	// 驗證配置
	if err := f.ValidateConfig(config); err != nil {
		return nil, gerror.Wrap(err, "config validation failed")
	}

	// 創建實例
	instance, err := f.CreateEmbedding(config.Provider)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to create embedding instance")
	}

	// 初始化實例
	ctx := context.Background()
	if err := instance.Initialize(ctx, config); err != nil {
		return nil, gerror.Wrap(err, "failed to initialize embedding instance")
	}

	return instance, nil
}

// GetSupportedProviders 獲取支持的提供商列表
func (f *DefaultFactory) GetSupportedProviders() []string {
	return []string{
		consts.EmbeddingAOAI,
		// 未來添加其他提供商
		// consts.EmbeddingOpenAI,
		// consts.EmbeddingVertexAI,
		// consts.EmbeddingHuggingFace,
		// consts.EmbeddingLocal,
	}
}

// ValidateConfig 驗證配置
func (f *DefaultFactory) ValidateConfig(config *embedding.EmbeddingConfig) error {
	if config == nil {
		return gerror.New("embedding config cannot be nil")
	}

	// 基本字段驗證
	if config.Provider == "" {
		return gerror.New("provider is required")
	}

	if config.Model == "" {
		return gerror.New("model is required")
	}

	// 檢查提供商是否支持
	supportedProviders := f.GetSupportedProviders()
	if !gstr.InArray(supportedProviders, config.Provider) {
		return gerror.Newf("unsupported provider: %s", config.Provider)
	}

	// 根據提供商類型進行特定驗證
	switch config.Provider {
	case consts.EmbeddingAOAI:
		return f.validateAOAIConfig(config)
	case consts.EmbeddingOpenAI:
		return f.validateOpenAIConfig(config)
	case consts.EmbeddingVertexAI:
		return f.validateVertexAIConfig(config)
	default:
		return gerror.Newf("unsupported provider for validation: %s", config.Provider)
	}
}

// validateAOAIConfig 驗證 Azure OpenAI 配置
func (f *DefaultFactory) validateAOAIConfig(config *embedding.EmbeddingConfig) error {
	// Azure OpenAI 特定驗證
	if config.BaseURL == "" {
		return gerror.New("BaseURL is required for Azure OpenAI")
	}

	if config.APIKey == "" {
		return gerror.New("APIKey is required for Azure OpenAI")
	}

	if config.APIVersion == "" {
		return gerror.New("APIVersion is required for Azure OpenAI")
	}

	// 檢查模型是否支持
	supportedModels := []string{
		"text-embedding-ada-002",
		"text-embedding-3-small",
		"text-embedding-3-large",
	}

	if !gstr.InArray(supportedModels, config.Model) {
		g.Log().Cat(consts.CatalogEmbedding).Warningf(nil,
			"Model %s may not be supported by Azure OpenAI, supported models: %v",
			config.Model, supportedModels)
	}

	// 設置默認值
	f.setDefaultValues(config)

	return nil
}

// validateOpenAIConfig 驗證 OpenAI 配置
func (f *DefaultFactory) validateOpenAIConfig(config *embedding.EmbeddingConfig) error {
	// OpenAI 特定驗證
	if config.APIKey == "" {
		return gerror.New("APIKey is required for OpenAI")
	}

	// BaseURL 對於 OpenAI 是可選的，有默認值
	if config.BaseURL == "" {
		config.BaseURL = "https://api.openai.com/v1"
	}

	// 設置默認值
	f.setDefaultValues(config)

	return nil
}

// validateVertexAIConfig 驗證 Vertex AI 配置
func (f *DefaultFactory) validateVertexAIConfig(config *embedding.EmbeddingConfig) error {
	// Vertex AI 特定驗證
	if config.BaseURL == "" {
		return gerror.New("BaseURL is required for Vertex AI")
	}
	// Vertex AI 通常使用服務帳戶認證，不需要 API Key

	// 設置默認值
	f.setDefaultValues(config)

	return nil
}

// setDefaultValues 設置默認值
func (f *DefaultFactory) setDefaultValues(config *embedding.EmbeddingConfig) {
	if config.MaxBatchSize <= 0 {
		config.MaxBatchSize = consts.EmbeddingDefaultBatchSize
	}

	if config.Timeout <= 0 {
		config.Timeout = consts.EmbeddingDefaultTimeout
	}

	if config.MaxRetries <= 0 {
		config.MaxRetries = consts.EmbeddingMaxRetryAttempts
	}

	if config.RetryDelay <= 0 {
		config.RetryDelay = consts.EmbeddingRetryDelaySecond
	}

	if config.Dimensions <= 0 {
		config.Dimensions = embedding.GetModelDimensions(config.Model)
	}
}

// GetEmbeddingConfigFromLLMParams 從 LLMParams 提取 embedding 配置
// 充分利用現有的 EmbeddingModel 字段，保持向後兼容
func GetEmbeddingConfigFromLLMParams(llmParams interface{}) (*embedding.EmbeddingConfig, error) {
	// 使用適配器處理 LLMParams
	adapter := GetAdapter()

	// 類型斷言
	params, ok := llmParams.(*model.LLMParams)
	if !ok {
		return nil, gerror.New("invalid LLMParams type")
	}

	return adapter.ConvertToEmbeddingConfig(params)
}

// 全局工廠實例
var globalFactory EmbeddingFactory

// GetFactory 獲取全局工廠實例
func GetFactory() EmbeddingFactory {
	if globalFactory == nil {
		globalFactory = NewFactory()
	}
	return globalFactory
}

// SetFactory 設置全局工廠實例（主要用於測試）
func SetFactory(factory EmbeddingFactory) {
	globalFactory = factory
}

// CreateEmbedding 便捷函數：創建未初始化的 embedding 實例
// 注意：返回的實例需要調用 Initialize() 方法才能使用
// 建議使用 CreateEmbeddingWithConfig() 方法
func CreateEmbedding(provider string) (IEmbeddings, error) {
	return GetFactory().CreateEmbedding(provider)
}

// CreateEmbeddingWithConfig 便捷函數：創建並初始化 embedding 實例
func CreateEmbeddingWithConfig(config *embedding.EmbeddingConfig) (IEmbeddings, error) {
	return GetFactory().CreateEmbeddingWithConfig(config)
}

// ValidateConfig 便捷函數：驗證配置
func ValidateConfig(config *embedding.EmbeddingConfig) error {
	return GetFactory().ValidateConfig(config)
}
