package clients

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os/exec"
	"sync"
	"time"

	"brainHub/internal/llms/common"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// MCPRequest MCP 请求结构
type MCPRequest struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

// MCPResponse MCP 响应结构
type MCPResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *MCPError   `json:"error,omitempty"`
}

// MCPError MCP 错误结构
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// StdioMCPClient STDIO MCP 客戶端實現
type StdioMCPClient struct {
	command   string
	args      []string
	env       []string
	process   *exec.Cmd
	stdin     io.WriteCloser
	stdout    io.ReadCloser
	logger    glog.ILogger
	mutex     sync.RWMutex
	connected bool
	timeout   time.Duration
	requestID int
}

// NewStdioMCPClient 創建新的 STDIO MCP 客戶端
func NewStdioMCPClient(command string, env []string, args ...string) *StdioMCPClient {
	return &StdioMCPClient{
		command: command,
		args:    args,
		env:     env,
		timeout: 30 * time.Second,
		logger:  g.Log(),
	}
}

// Connect 連接到 MCP 服務器
func (c *StdioMCPClient) Connect(ctx context.Context) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.connected {
		return nil
	}

	c.logger.Infof(ctx, "Connecting to STDIO MCP server: %s %v", c.command, c.args)

	// 創建進程
	c.process = exec.CommandContext(ctx, c.command, c.args...)
	c.process.Env = c.env

	// 設置管道
	stdin, err := c.process.StdinPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdin pipe: %v", err)
	}
	c.stdin = stdin

	stdout, err := c.process.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %v", err)
	}
	c.stdout = stdout

	// 啟動進程
	if err := c.process.Start(); err != nil {
		return fmt.Errorf("failed to start process: %v", err)
	}

	c.connected = true
	c.logger.Infof(ctx, "Successfully connected to STDIO MCP server")
	return nil
}

// ListTools 列出可用工具
func (c *StdioMCPClient) ListTools(ctx context.Context) ([]common.ToolDefinition, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 發送 list_tools 請求
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  "tools/list",
	}

	response, err := c.sendRequest(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to list tools: %v", err)
	}

	// 解析響應
	var tools []common.ToolDefinition
	if response.Result != nil {
		// 簡化的解析邏輯
		if resultMap, ok := response.Result.(map[string]interface{}); ok {
			if toolsArray, ok := resultMap["tools"].([]interface{}); ok {
				for _, toolData := range toolsArray {
					if toolMap, ok := toolData.(map[string]interface{}); ok {
						tool := common.ToolDefinition{
							Name:        getString(toolMap, "name"),
							Description: getString(toolMap, "description"),
							Parameters:  getMap(toolMap, "input_schema"),
						}
						tools = append(tools, tool)
					}
				}
			}
		}
	}

	return tools, nil
}

// CallTool 調用工具
func (c *StdioMCPClient) CallTool(ctx context.Context, toolName string, args map[string]interface{}) (*common.ToolResult, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected {
		return nil, fmt.Errorf("client not connected")
	}

	// 發送 call_tool 請求
	request := MCPRequest{
		JSONRPC: "2.0",
		ID:      c.getNextRequestID(),
		Method:  "tools/call",
		Params: map[string]interface{}{
			"name":      toolName,
			"arguments": args,
		},
	}

	response, err := c.sendRequest(ctx, request)
	if err != nil {
		return &common.ToolResult{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 解析響應
	result := &common.ToolResult{
		Success: response.Error == nil,
	}

	if response.Error != nil {
		result.Error = response.Error.Message
	} else if response.Result != nil {
		if resultMap, ok := response.Result.(map[string]interface{}); ok {
			if content, ok := resultMap["content"].(string); ok {
				result.Content = content
			} else {
				// 如果沒有 content 字段，將整個結果序列化為字符串
				if data, err := json.Marshal(response.Result); err == nil {
					result.Content = string(data)
				}
			}
		}
	}

	return result, nil
}

// Close 關閉客戶端
func (c *StdioMCPClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return nil
	}

	c.connected = false

	// 關閉管道
	if c.stdin != nil {
		c.stdin.Close()
	}
	if c.stdout != nil {
		c.stdout.Close()
	}

	// 終止進程
	if c.process != nil {
		if err := c.process.Process.Kill(); err != nil {
			c.logger.Errorf(context.Background(), "Failed to kill process: %v", err)
		}
		c.process.Wait()
	}

	c.logger.Info(context.Background(), "STDIO MCP client closed")
	return nil
}

// sendRequest 發送請求並接收響應
func (c *StdioMCPClient) sendRequest(ctx context.Context, request MCPRequest) (*MCPResponse, error) {
	// 序列化請求
	data, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	// 發送請求
	if _, err := c.stdin.Write(append(data, '\n')); err != nil {
		return nil, fmt.Errorf("failed to write request: %v", err)
	}

	// 讀取響應
	scanner := bufio.NewScanner(c.stdout)
	if !scanner.Scan() {
		return nil, fmt.Errorf("failed to read response")
	}

	// 解析響應
	var response MCPResponse
	if err := json.Unmarshal(scanner.Bytes(), &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return &response, nil
}

// getNextRequestID 獲取下一個請求 ID
func (c *StdioMCPClient) getNextRequestID() int {
	c.requestID++
	return c.requestID
}

// 輔助函數
func getString(m map[string]interface{}, key string) string {
	if v, ok := m[key].(string); ok {
		return v
	}
	return ""
}

func getMap(m map[string]interface{}, key string) map[string]interface{} {
	if v, ok := m[key].(map[string]interface{}); ok {
		return v
	}
	return nil
}
