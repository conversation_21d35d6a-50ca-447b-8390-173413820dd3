package mcp

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// ConfigManager MCP 配置管理器
type ConfigManager struct {
	logger glog.ILogger
}

// NewConfigManager 創建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		logger: g.Log().Cat("MCPConfig"),
	}
}

// LoadMCPConfig 從 Nacos 加載 MCP 配置
func (cm *ConfigManager) LoadMCPConfig(ctx context.Context) (*MCPConfig, error) {
	cm.logger.Info(ctx, "Loading MCP configuration from Nacos")

	// 檢查 MCP 是否啟用
	enabled, err := cm.isMCPEnabled(ctx)
	if err != nil {
		return nil, NewConfigInvalidError(fmt.Sprintf("failed to check MCP enabled status: %v", err))
	}

	if !enabled {
		cm.logger.Info(ctx, "MCP is disabled in configuration")
		return &MCPConfig{Enabled: false}, nil
	}

	// 加載服務器配置
	servers, err := cm.loadServerConfigs(ctx)
	if err != nil {
		return nil, NewConfigInvalidError(fmt.Sprintf("failed to load server configs: %v", err))
	}

	// 加載全局配置
	globalConfig, err := cm.loadGlobalConfig(ctx)
	if err != nil {
		return nil, NewConfigInvalidError(fmt.Sprintf("failed to load global config: %v", err))
	}

	config := &MCPConfig{
		Enabled: true,
		Servers: servers,
		Global:  globalConfig,
	}

	// 驗證配置
	if err := cm.validateConfig(config); err != nil {
		return nil, NewConfigInvalidError(fmt.Sprintf("config validation failed: %v", err))
	}

	cm.logger.Infof(ctx, "Successfully loaded MCP configuration with %d servers", len(servers))
	return config, nil
}

// ValidateConfig 驗證 MCP 配置
func ValidateConfig(config *MCPConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// 驗證服務器配置
	for i, server := range config.Servers {
		if err := validateServerConfig(server); err != nil {
			return fmt.Errorf("server %d validation failed: %v", i, err)
		}
	}

	// 驗證全局配置
	if err := validateGlobalConfig(config.Global); err != nil {
		return fmt.Errorf("global config validation failed: %v", err)
	}

	return nil
}

// validateServerConfig 驗證服務器配置
func validateServerConfig(config MCPServerConfig) error {
	if config.Name == "" {
		return fmt.Errorf("server name cannot be empty")
	}

	if config.Type == "" {
		return fmt.Errorf("server type cannot be empty")
	}

	// 驗證服務器類型
	validTypes := map[string]bool{
		"stdio": true,
		"http":  true,
		"sse":   true,
	}
	if !validTypes[config.Type] {
		return fmt.Errorf("invalid server type: %s", config.Type)
	}

	// 根據類型驗證特定配置
	switch config.Type {
	case "stdio":
		if config.Command == "" {
			return fmt.Errorf("command cannot be empty for stdio server")
		}
	case "http", "sse":
		if config.URL == "" {
			return fmt.Errorf("URL cannot be empty for %s server", config.Type)
		}
	}

	// 驗證超時格式
	if config.Timeout != "" {
		if _, err := time.ParseDuration(config.Timeout); err != nil {
			return fmt.Errorf("invalid timeout format: %s", config.Timeout)
		}
	}

	// 驗證重試次數
	if config.RetryCount < 0 {
		return fmt.Errorf("retry count cannot be negative")
	}

	return nil
}

// validateGlobalConfig 驗證全局配置
func validateGlobalConfig(config MCPGlobalConfig) error {
	// 驗證默認超時
	if config.DefaultTimeout != "" {
		if _, err := time.ParseDuration(config.DefaultTimeout); err != nil {
			return fmt.Errorf("invalid default timeout format: %s", config.DefaultTimeout)
		}
	}

	// 驗證緩存 TTL
	if config.CacheTTL != "" {
		if _, err := time.ParseDuration(config.CacheTTL); err != nil {
			return fmt.Errorf("invalid cache TTL format: %s", config.CacheTTL)
		}
	}

	// 驗證並發調用數
	if config.MaxConcurrentCalls < 0 {
		return fmt.Errorf("max concurrent calls cannot be negative")
	}

	// 驗證日誌級別
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if config.LogLevel != "" && !validLogLevels[config.LogLevel] {
		return fmt.Errorf("invalid log level: %s", config.LogLevel)
	}

	return nil
}

// isMCPEnabled 檢查 MCP 是否啟用
func (cm *ConfigManager) isMCPEnabled(ctx context.Context) (bool, error) {
	vEnabled, err := g.Cfg().Get(ctx, "mcp_config.enabled", false)
	if err != nil {
		return false, err
	}

	return vEnabled.Bool(), nil
}

// loadServerConfigs 加載服務器配置
func (cm *ConfigManager) loadServerConfigs(ctx context.Context) ([]MCPServerConfig, error) {
	vServers, err := g.Cfg().Get(ctx, "mcp_servers")
	if err != nil {
		return nil, err
	}

	if vServers.IsEmpty() {
		cm.logger.Warning(ctx, "No MCP servers configured")
		return []MCPServerConfig{}, nil
	}

	var servers []MCPServerConfig
	if err := vServers.Struct(&servers); err != nil {
		return nil, fmt.Errorf("failed to parse server configs: %v", err)
	}

	// 為每個服務器設置默認值
	for i := range servers {
		cm.setServerDefaults(&servers[i])
	}

	return servers, nil
}

// loadGlobalConfig 加載全局配置
func (cm *ConfigManager) loadGlobalConfig(ctx context.Context) (MCPGlobalConfig, error) {
	vGlobal, err := g.Cfg().Get(ctx, "mcp_config")
	if err != nil {
		cm.logger.Errorf(ctx, "Failed to get global MCP config: %v", err)
		// 返回默認配置
		return cm.getDefaultGlobalConfig(), nil
	}

	var globalConfig MCPGlobalConfig
	if err := vGlobal.Struct(&globalConfig); err != nil {
		cm.logger.Errorf(ctx, "Failed to parse global MCP config: %v", err)
		return cm.getDefaultGlobalConfig(), nil
	}

	// 設置默認值
	cm.setGlobalDefaults(&globalConfig)

	return globalConfig, nil
}

// setServerDefaults 設置服務器配置默認值
func (cm *ConfigManager) setServerDefaults(config *MCPServerConfig) {
	if config.Timeout == "" {
		config.Timeout = DefaultTimeout
	}

	if config.RetryCount == 0 {
		config.RetryCount = DefaultRetryCount
	}

	// 驗證客戶端類型
	if config.Type == "" {
		config.Type = ClientTypeStdio // 默認為 stdio
	}

	// 根據類型設置特定默認值
	switch config.Type {
	case ClientTypeStdio:
		if config.Command == "" {
			cm.logger.Warningf(context.Background(), "STDIO client %s has no command specified", config.Name)
		}
	case ClientTypeHTTP, ClientTypeSSE:
		if config.URL == "" {
			cm.logger.Warningf(context.Background(), "%s client %s has no URL specified", config.Type, config.Name)
		}
	}
}

// setGlobalDefaults 設置全局配置默認值
func (cm *ConfigManager) setGlobalDefaults(config *MCPGlobalConfig) {
	if config.DefaultTimeout == "" {
		config.DefaultTimeout = DefaultTimeout
	}

	if config.MaxConcurrentCalls == 0 {
		config.MaxConcurrentCalls = DefaultMaxConcurrentCalls
	}

	if config.CacheTTL == "" {
		config.CacheTTL = DefaultCacheTTL
	}

	if config.LogLevel == "" {
		config.LogLevel = "info"
	}

	// 確保啟用狀態
	config.Enabled = true
}

// getDefaultGlobalConfig 獲取默認全局配置
func (cm *ConfigManager) getDefaultGlobalConfig() MCPGlobalConfig {
	return MCPGlobalConfig{
		Enabled:            true,
		DefaultTimeout:     DefaultTimeout,
		MaxConcurrentCalls: DefaultMaxConcurrentCalls,
		CacheTTL:           DefaultCacheTTL,
		LogLevel:           "info",
	}
}

// validateConfig 驗證配置
func (cm *ConfigManager) validateConfig(config *MCPConfig) error {
	if config == nil {
		return fmt.Errorf("config is nil")
	}

	if !config.Enabled {
		return nil // 如果未啟用，不需要驗證
	}

	// 驗證全局配置
	if err := cm.validateGlobalConfig(&config.Global); err != nil {
		return fmt.Errorf("global config validation failed: %v", err)
	}

	// 驗證服務器配置
	serverNames := make(map[string]bool)
	for i, server := range config.Servers {
		if err := cm.validateServerConfig(&server); err != nil {
			return fmt.Errorf("server config %d validation failed: %v", i, err)
		}

		// 檢查服務器名稱唯一性
		if serverNames[server.Name] {
			return fmt.Errorf("duplicate server name: %s", server.Name)
		}
		serverNames[server.Name] = true
	}

	return nil
}

// validateGlobalConfig 驗證全局配置
func (cm *ConfigManager) validateGlobalConfig(config *MCPGlobalConfig) error {
	// 驗證超時時間
	if _, err := time.ParseDuration(config.DefaultTimeout); err != nil {
		return fmt.Errorf("invalid default timeout: %s", config.DefaultTimeout)
	}

	// 驗證緩存 TTL
	if _, err := time.ParseDuration(config.CacheTTL); err != nil {
		return fmt.Errorf("invalid cache TTL: %s", config.CacheTTL)
	}

	// 驗證並發數
	if config.MaxConcurrentCalls <= 0 {
		return fmt.Errorf("max concurrent calls must be positive: %d", config.MaxConcurrentCalls)
	}

	// 驗證日誌級別
	validLogLevels := []string{"debug", "info", "warning", "error"}
	isValidLogLevel := false
	for _, level := range validLogLevels {
		if strings.ToLower(config.LogLevel) == level {
			isValidLogLevel = true
			break
		}
	}
	if !isValidLogLevel {
		return fmt.Errorf("invalid log level: %s", config.LogLevel)
	}

	return nil
}

// validateServerConfig 驗證服務器配置
func (cm *ConfigManager) validateServerConfig(config *MCPServerConfig) error {
	// 驗證名稱
	if config.Name == "" {
		return fmt.Errorf("server name cannot be empty")
	}

	// 驗證類型
	validTypes := []string{ClientTypeStdio, ClientTypeHTTP, ClientTypeSSE}
	isValidType := false
	for _, validType := range validTypes {
		if config.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid client type: %s", config.Type)
	}

	// 驗證超時時間
	if _, err := time.ParseDuration(config.Timeout); err != nil {
		return fmt.Errorf("invalid timeout: %s", config.Timeout)
	}

	// 驗證重試次數
	if config.RetryCount < 0 {
		return fmt.Errorf("retry count cannot be negative: %d", config.RetryCount)
	}

	// 根據類型驗證特定配置
	switch config.Type {
	case ClientTypeStdio:
		if config.Command == "" {
			return fmt.Errorf("STDIO client requires command")
		}
	case ClientTypeHTTP, ClientTypeSSE:
		if config.URL == "" {
			return fmt.Errorf("%s client requires URL", config.Type)
		}
		if !strings.HasPrefix(config.URL, "http://") && !strings.HasPrefix(config.URL, "https://") {
			return fmt.Errorf("invalid URL format: %s", config.URL)
		}
	}

	return nil
}

// GetServerConfig 根據名稱獲取服務器配置
func (cm *ConfigManager) GetServerConfig(config *MCPConfig, serverName string) (*MCPServerConfig, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	for _, server := range config.Servers {
		if server.Name == serverName {
			return &server, nil
		}
	}

	return nil, NewClientNotFoundError(serverName)
}

// ListServerNames 列出所有服務器名稱
func (cm *ConfigManager) ListServerNames(config *MCPConfig) []string {
	if config == nil {
		return []string{}
	}

	names := make([]string, len(config.Servers))
	for i, server := range config.Servers {
		names[i] = server.Name
	}

	return names
}

// GetServersByType 根據類型獲取服務器配置
func (cm *ConfigManager) GetServersByType(config *MCPConfig, clientType string) []MCPServerConfig {
	if config == nil {
		return []MCPServerConfig{}
	}

	var servers []MCPServerConfig
	for _, server := range config.Servers {
		if server.Type == clientType {
			servers = append(servers, server)
		}
	}

	return servers
}

// ParseTimeout 解析超時時間
func (cm *ConfigManager) ParseTimeout(timeout string) (time.Duration, error) {
	if timeout == "" {
		return time.ParseDuration(DefaultTimeout)
	}

	return time.ParseDuration(timeout)
}

// ExpandEnvironmentVariables 展開環境變量
func (cm *ConfigManager) ExpandEnvironmentVariables(config *MCPServerConfig) {
	// 展開 Headers 中的環境變量
	for key, value := range config.Headers {
		if strings.HasPrefix(value, "${") && strings.HasSuffix(value, "}") {
			envVar := value[2 : len(value)-1]
			if envValue := g.Cfg().MustGet(context.Background(), envVar).String(); envValue != "" {
				config.Headers[key] = envValue
			}
		}
	}

	// 展開 URL 中的環境變量
	if strings.Contains(config.URL, "${") {
		// 簡單的環境變量替換
		url := config.URL
		// 這裡可以添加更複雜的環境變量替換邏輯
		config.URL = url
	}
}

// CreateConfigSummary 創建配置摘要
func (cm *ConfigManager) CreateConfigSummary(config *MCPConfig) string {
	if config == nil {
		return "MCP Config: nil"
	}

	if !config.Enabled {
		return "MCP Config: Disabled"
	}

	summary := fmt.Sprintf("MCP Config: Enabled, %d servers", len(config.Servers))

	// 按類型統計服務器
	typeCounts := make(map[string]int)
	for _, server := range config.Servers {
		typeCounts[server.Type]++
	}

	var typeInfo []string
	for clientType, count := range typeCounts {
		typeInfo = append(typeInfo, fmt.Sprintf("%s: %d", clientType, count))
	}

	if len(typeInfo) > 0 {
		summary += fmt.Sprintf(" (%s)", strings.Join(typeInfo, ", "))
	}

	return summary
}

// WatchConfigChanges 監聽配置變更（預留接口）
func (cm *ConfigManager) WatchConfigChanges(ctx context.Context, callback func(*MCPConfig)) error {
	// 這裡可以實現配置變更監聽邏輯
	// 例如監聽 Nacos 配置變更事件
	cm.logger.Info(ctx, "Config change watching is not implemented yet")
	return nil
}
