package test

import (
	"context"
	"testing"

	"brainHub/internal/llms/aoai"
	"brainHub/internal/llms/claude"
	"brainHub/internal/llms/gemini"
	"brainHub/internal/llms/mcp"
	"brainHub/internal/model/llm"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestAoAi_MCPIntegration 測試 Azure OpenAI 與 MCP 的集成
func TestAoAi_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 創建 Azure OpenAI 實例
		aoaiLLM := aoai.New()
		t.AssertNE(aoaiLLM, nil)
		
		// 創建測試配置（禁用 MCP 以避免實際連接）
		config := &llm.LLMsConfig{
			MCPConfig: &llm.MCPConfig{
				Enabled: false,
				Servers: []llm.MCPServerConfig{},
				Global: llm.MCPGlobalConfig{
					Enabled:            false,
					DefaultTimeout:     "30s",
					MaxConcurrentCalls: 10,
					CacheTTL:          "5m",
					LogLevel:          "info",
				},
			},
		}
		
		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}
		
		// 測試初始化（應該成功，即使沒有有效的 Azure OpenAI 配置）
		err := aoaiLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Azure OpenAI config): %v", err)
		}
		
		// 清理
		aoaiLLM.Release(ctx)
	})
}

// TestClaude_MCPIntegration 測試 Claude 與 MCP 的集成
func TestClaude_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 創建 Claude 實例
		claudeLLM := claude.New()
		t.AssertNE(claudeLLM, nil)
		
		// 創建測試配置（禁用 MCP）
		config := &llm.LLMsConfig{
			MCPConfig: &llm.MCPConfig{
				Enabled: false,
				Servers: []llm.MCPServerConfig{},
				Global: llm.MCPGlobalConfig{
					Enabled:            false,
					DefaultTimeout:     "30s",
					MaxConcurrentCalls: 10,
					CacheTTL:          "5m",
					LogLevel:          "info",
				},
			},
		}
		
		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}
		
		// 測試初始化
		err := claudeLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Claude config): %v", err)
		}
		
		// 清理
		claudeLLM.Release(ctx)
	})
}

// TestGemini_MCPIntegration 測試 Gemini 與 MCP 的集成
func TestGemini_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 創建 Gemini 實例
		geminiLLM := gemini.New()
		t.AssertNE(geminiLLM, nil)
		
		// 創建測試配置（禁用 MCP）
		config := &llm.LLMsConfig{
			MCPConfig: &llm.MCPConfig{
				Enabled: false,
				Servers: []llm.MCPServerConfig{},
				Global: llm.MCPGlobalConfig{
					Enabled:            false,
					DefaultTimeout:     "30s",
					MaxConcurrentCalls: 10,
					CacheTTL:          "5m",
					LogLevel:          "info",
				},
			},
		}
		
		// 創建測試 payload
		payload := &llm.Payload{
			SystemInstruction: "You are a helpful assistant.",
		}
		
		// 測試初始化
		err := geminiLLM.Initialize(ctx, config, payload)
		if err != nil {
			t.Logf("Initialize failed as expected (no valid Gemini config): %v", err)
		}
		
		// 清理
		geminiLLM.Release(ctx)
	})
}

// TestMCPConfig_Validation 測試 MCP 配置驗證
func TestMCPConfig_Validation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試有效配置
		validConfig := &llm.MCPConfig{
			Enabled: true,
			Servers: []llm.MCPServerConfig{
				{
					Name:        "test-server",
					Type:        "stdio",
					Description: "Test server",
					Command:     "echo",
					Args:        []string{"hello"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: llm.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		t.AssertEQ(validConfig.Enabled, true)
		t.AssertEQ(len(validConfig.Servers), 1)
		t.AssertEQ(validConfig.Servers[0].Name, "test-server")
		t.AssertEQ(validConfig.Servers[0].Type, "stdio")
		
		// 測試禁用配置
		disabledConfig := &llm.MCPConfig{
			Enabled: false,
			Servers: []llm.MCPServerConfig{},
			Global: llm.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		t.AssertEQ(disabledConfig.Enabled, false)
		t.AssertEQ(len(disabledConfig.Servers), 0)
	})
}

// TestMCPToolManager_Integration 測試 MCP 工具管理器集成
func TestMCPToolManager_Integration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 創建測試配置
		config := &mcp.MCPConfig{
			Enabled: false, // 禁用以避免實際連接
			Servers: []mcp.MCPServerConfig{},
			Global: mcp.MCPGlobalConfig{
				Enabled:            false,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		// 創建工具管理器
		manager := mcp.NewMCPToolManager(config)
		t.AssertNE(manager, nil)
		
		// 測試初始化
		err := manager.Initialize(ctx)
		t.AssertNil(err)
		
		// 測試獲取工具定義
		tools, err := manager.GetToolDefinitions(ctx)
		t.AssertNil(err)
		t.AssertEQ(len(tools), 0) // 應該為空，因為沒有配置服務器
		
		// 測試健康檢查
		health := manager.HealthCheck(ctx)
		t.AssertNE(health, nil)
		t.AssertEQ(health["tool_manager_status"], "healthy")
		
		// 清理
		err = manager.Close()
		t.AssertNil(err)
	})
}

// TestLLMRouter_MCPIntegration 測試 LLM Router 與 MCP 的集成
func TestLLMRouter_MCPIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 這個測試需要實際的 LLM Router 實例
		// 由於依賴複雜，這裡只做基本的配置測試
		
		// 測試 MCP 配置結構
		mcpConfig := &llm.MCPConfig{
			Enabled: true,
			Servers: []llm.MCPServerConfig{
				{
					Name:        "test-stdio",
					Type:        "stdio",
					Description: "Test STDIO server",
					Command:     "echo",
					Args:        []string{"hello"},
					Timeout:     "30s",
					RetryCount:  3,
				},
				{
					Name:        "test-http",
					Type:        "http",
					Description: "Test HTTP server",
					URL:         "http://localhost:8080/mcp",
					Headers:     map[string]string{"Authorization": "Bearer token"},
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: llm.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		// 驗證配置結構
		t.AssertEQ(mcpConfig.Enabled, true)
		t.AssertEQ(len(mcpConfig.Servers), 2)
		
		// 驗證 STDIO 服務器配置
		stdioServer := mcpConfig.Servers[0]
		t.AssertEQ(stdioServer.Name, "test-stdio")
		t.AssertEQ(stdioServer.Type, "stdio")
		t.AssertEQ(stdioServer.Command, "echo")
		t.AssertEQ(len(stdioServer.Args), 1)
		
		// 驗證 HTTP 服務器配置
		httpServer := mcpConfig.Servers[1]
		t.AssertEQ(httpServer.Name, "test-http")
		t.AssertEQ(httpServer.Type, "http")
		t.AssertEQ(httpServer.URL, "http://localhost:8080/mcp")
		t.AssertEQ(len(httpServer.Headers), 1)
	})
}

// TestMCPErrorHandling 測試 MCP 錯誤處理
func TestMCPErrorHandling(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 測試無效的服務器配置
		invalidConfig := &mcp.MCPConfig{
			Enabled: true,
			Servers: []mcp.MCPServerConfig{
				{
					Name:        "invalid-server",
					Type:        "invalid-type",
					Description: "Invalid server",
					Timeout:     "30s",
					RetryCount:  3,
				},
			},
			Global: mcp.MCPGlobalConfig{
				Enabled:            true,
				DefaultTimeout:     "30s",
				MaxConcurrentCalls: 10,
				CacheTTL:          "5m",
				LogLevel:          "info",
			},
		}
		
		// 創建工具管理器
		manager := mcp.NewMCPToolManager(invalidConfig)
		t.AssertNE(manager, nil)
		
		// 初始化應該處理無效配置
		err := manager.Initialize(ctx)
		if err != nil {
			t.Logf("Initialize failed as expected for invalid config: %v", err)
		}
		
		// 即使初始化失敗，也應該能夠安全關閉
		err = manager.Close()
		t.AssertNil(err)
	})
}

// BenchmarkMCPIntegration 性能測試
func BenchmarkMCPIntegration(b *testing.B) {
	ctx := context.Background()
	
	config := &mcp.MCPConfig{
		Enabled: false,
		Servers: []mcp.MCPServerConfig{},
		Global: mcp.MCPGlobalConfig{
			Enabled:            false,
			DefaultTimeout:     "30s",
			MaxConcurrentCalls: 10,
			CacheTTL:          "5m",
			LogLevel:          "info",
		},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager := mcp.NewMCPToolManager(config)
		_ = manager.Initialize(ctx)
		_, _ = manager.GetToolDefinitions(ctx)
		_ = manager.Close()
	}
}
