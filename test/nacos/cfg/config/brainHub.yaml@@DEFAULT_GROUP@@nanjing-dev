server:
  address: "************:8088"
  clientMaxBodySize: 200M
  openapiPath: "/api.json"
  swaggerPath: "/swagger"


logger:
  level: "all"
  stdout: true
  path: ./logs
  file: brainHub_{Y-m-d}.log
  RotateExpire: "1d"
  RotateBackupLimit: 1
  RotateBackupExpire: "7d"
  RotateBackupCompress: 9


system:
  # time duration of retry to send to ai
  ai_send_retry_ttl: 40s
  # 最久時長 ，最久没有调用ai 的時長。如果没有调用则返回到 Pool 中再次利用
  ai_reserve_ttl: 1h
  # 在没有为 tenant 设置llm 所使用的预设 LLM
  default_ai: gemini
  # chat history 默認提取幾天內的聊天記錄信息
  chat_message:
     # 在每次對話前提取n天內的對話記錄做為上下文
    recent_duration: 3d
  # 附件下載到的臨時folder下
  asset_path: "./asset"  
  # asset management service 服務名稱和scheme 設定
  asset_management:
    name: ams.svc
    scheme: http
  # data sync hub servcie name and scheme
  data_sync:
    name: dsh.svc
    scheme: http
rabbitMQ:
  url: "************************************/"
redis:
  default:
    address: ************:6379
    db: 0


prompts:
  # chat 系统指令
  # chat_system_instruction: >
  #   # 角色定义
  #   你是一個厲害的 AI 助理。 我會提供給你一些資料。你所有的回復都要參照我提供的各種文件或者 youtube 鏈接。
  #   # 要求
  #   * 所有回復都要參照我提供的各種文件和youtube 鏈接
  #   *  如果所提供的資料也無法回答我的問題。則用委婉的方式進行說明 （回復要讓我信服和共情）
  #   *  判斷用戶的意圖 ，如果與所有資料都無關，則可以參考 我給的文件，給客戶一些建議的問題(quick reply）
  #   *  判斷用戶意圖， 只處理用戶的意圖是 ** 詢問問題 ** ， 其他都要婉拒
  #   *  在回復的時候不要提及自己是 ai 助理的角色
  #   *  在回答時候，不要提供參考的文件或者鏈接 
  #   *  再做回復的時候，不要提示說根據某一個文件這樣的字眼
  #   *  你只是对照资料回答问题其他的一概不要做(例如： 编写脚本等等类似问题 ）  
  #   # 輸出格式
  #   輸出採用 json 格式輸出。請直接數據不要帶任何語言標籤 ，不要廢話。 格式如下

  #   [
  #   	{
  #   		"quickReply": {                               // 依照我的問題推測接下來我感興趣的 3 個問題  (items 有三組） 
  #   			"items": [
  #   				{
  #   					"action": {                 // data,display ,title保持相同值
  #   						"data": "xxx",            
  #   						"displayText": "xxx",
  #   						"title": "xxx",
  #   						"type": "Postback"      // 固定值
  #   					},
  #   					"type": "Action"       // 固定值
  #   				},
  #   				... 
  #   			]
  #   		},
  #   		"text": "xxx",      //  回復我的信息 
  #   		"type": "Text"  // 固定值
  #   	}
  #   ]
  # line 应用系统指令
  # line_system_instruction: >
  #   # 角色和职能定义
  #       你是一个厉害的 AI  助理但是只会针对名片做识别,工時日志建立和問候信撰寫三种工作。 
  #       # 要求 
  #          每次用户的输入，你要先做意图判断。如果判断用户的意图和你的职能相关则给与执行 
  #          否则要提示用户你的职能（撰寫問候信這個不要說）。请用户做正确的输入 。 如果以下输入中必要元素不存在要提示用户要带有这些信息才可以。
  #          今天的日期是： {{.now_date}} 
    
  #       # 名片识别
  #        用户要上传一张带有名片的图片。 
  #        輸出內容字數控制在 250以內
  #       # 工時日志建立
  #        建立活动的必要元素包括 
  #        时间、地点、事件。 这几个是必要元素。建立活动一定要有。 
  #        輸出內容字數控制在 250以內

  #       # 名片输出格式
  #        名片识别要包含 '公司',' 姓名' '职务', 'e-mail' , '电话' ,‘行動電話’ ，‘公司地址’,'傳真'  
  #        采用 json格式输出，不要廢話直接輸出 json  不要添加任何语言标签(不要以```json開頭， 不要以```結尾）  。直接输出格式如下  ( 如果没有识别到以下信息则留空白） 
  #       {  
  #          "type": "card"  , // 固定值表示名片識別
  #          "company":"xxxx" , // 公司名稱   
  #          "name":"xxxx" ,    // 姓名
  #          "occupation":"xxxx",  // 職務
  #          "phone":"xxx",    // 電話 （固定電話）
  #          "mobile_phone":"xxx", //行動電話（手機號碼）
  #          "fax":"xxx",  // 傳真
  #          "eMail":"xxxx",  // 電子郵件
  #          "address":"xxxx"  // 公司地址
  #   }


  #       # 活动输出格式  
  #       采用 json 格式输出，不要廢話，直接輸出 json 不要帶任何語言標籤(不要以```json開頭， 不要以```結尾） 

  #       {    
  #             "type":"activity" , // 固定值表示活動日誌
  #             "date_time":"xxx" , // 年月日时的格式 (yyyy-MM-dd H:m:s) 
  #             "location":"xxx" , //地点  
  #             "related_persons":["xxx" , "xx" ] , //活動關聯的關係人可以是多個
  #             "background_story":"xxx"  // 活动的具体描述 。 
  #       }
  #      # 撰寫問候信
  #       這部分要求我會隨時提供給你




#  默認的LLM （ gemini） 相關參數
default_llms:
  common:
    temperature: 1.0
    max_output_tokens: 4096

  vertex:
    project_id: aile-ai-development
    region: us-central1
    credential_file: ./key/key.json
    gemini:
      model: gemini-2.5-flash-preview-04-17
      temperature: 1.0
      max_output_ tokens: 2000
      include_thoughts: false
      thinking_budget: 0

