2025-07-07T16:46:49.890+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-07T16:46:49.890+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55062
2025-07-07T16:46:49.890+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c441009f-b584-4bce-91df-a9a52178b16d)
2025-07-07T16:46:49.890+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:46:49.890+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:46:49.890+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:46:49.891+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c441009f-b584-4bce-91df-a9a52178b16d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:46:49.891+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:46:49.891+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:46:49.891+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:46:49.891+0800	INFO	util/common.go:96	Local IP:************
2025-07-07T16:46:50.014+0800	INFO	rpc/rpc_client.go:337	c441009f-b584-4bce-91df-a9a52178b16d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878010000_192.168.3.3_61360
2025-07-07T16:53:51.807+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-07T16:53:51.807+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55310
2025-07-07T16:53:51.807+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=192a8ca5-2f47-421e-bf57-6563db06ee31)
2025-07-07T16:53:51.808+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:53:51.808+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:53:51.808+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:53:51.808+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 192a8ca5-2f47-421e-bf57-6563db06ee31 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:53:51.808+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:53:51.808+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:53:51.808+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:53:51.808+0800	INFO	util/common.go:96	Local IP:************
2025-07-07T16:53:51.929+0800	INFO	rpc/rpc_client.go:337	192a8ca5-2f47-421e-bf57-6563db06ee31 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878431893_192.168.3.3_62027
2025-07-07T16:54:39.075+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-07T16:54:39.076+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55573
2025-07-07T16:54:39.076+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f63b44aa-76ac-4cbf-a76d-466cc5a109b0)
2025-07-07T16:54:39.076+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:54:39.076+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:54:39.076+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:54:39.076+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f63b44aa-76ac-4cbf-a76d-466cc5a109b0 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:54:39.076+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:54:39.076+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:54:39.076+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:54:39.076+0800	INFO	util/common.go:96	Local IP:************
2025-07-07T16:54:39.199+0800	INFO	rpc/rpc_client.go:337	f63b44aa-76ac-4cbf-a76d-466cc5a109b0 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878479163_192.168.3.3_62128
2025-07-07T16:55:24.396+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-07T16:55:24.397+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55941
2025-07-07T16:55:24.397+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=178fdefa-a194-47bb-8184-a522518cbc7f)
2025-07-07T16:55:24.397+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:55:24.397+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:55:24.397+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:55:24.397+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 178fdefa-a194-47bb-8184-a522518cbc7f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:55:24.397+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:55:24.397+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:55:24.397+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:55:24.398+0800	INFO	util/common.go:96	Local IP:************
2025-07-07T16:55:24.520+0800	INFO	rpc/rpc_client.go:337	178fdefa-a194-47bb-8184-a522518cbc7f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878524483_192.168.3.3_62219
2025-07-07T16:56:32.267+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-07T16:56:32.268+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55652
2025-07-07T16:56:32.268+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=3d8da740-67de-49b3-a4ae-5e843c9c59ad)
2025-07-07T16:56:32.268+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:56:32.268+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:56:32.268+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:56:32.268+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 3d8da740-67de-49b3-a4ae-5e843c9c59ad try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:56:32.268+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:56:32.268+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:56:32.268+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:56:32.268+0800	INFO	util/common.go:96	Local IP:************
2025-07-07T16:56:32.393+0800	INFO	rpc/rpc_client.go:337	3d8da740-67de-49b3-a4ae-5e843c9c59ad success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878592355_192.168.3.3_62340
2025-07-17T16:45:12.565+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-17T16:45:12.566+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55689
2025-07-17T16:45:12.566+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d941d741-60f1-464a-9f52-2384db287ed0)
2025-07-17T16:45:12.566+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T16:45:12.566+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T16:45:12.566+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T16:45:12.566+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d941d741-60f1-464a-9f52-2384db287ed0 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T16:45:12.566+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T16:45:12.566+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T16:45:12.566+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T16:45:12.567+0800	INFO	util/common.go:96	Local IP:************
2025-07-17T16:45:12.683+0800	INFO	rpc/rpc_client.go:337	d941d741-60f1-464a-9f52-2384db287ed0 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752741912661_192.168.3.3_52944
2025-07-17T16:45:12.684+0800	INFO	rpc/rpc_client.go:486	d941d741-60f1-464a-9f52-2384db287ed0 notify connected event to listeners , connectionId=1752741912661_192.168.3.3_52944
